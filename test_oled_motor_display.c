/**
 * @file test_oled_motor_display.c
 * @brief OLED电机速度显示测试程序
 * @details 测试OLED显示电机实时速度功能
 * <AUTHOR> Assistant
 * @date 2025-08-01
 */

#include "SysConfig.h"

// 测试用的模拟电机速度数据
static float test_left_speed = 0.0f;
static float test_right_speed = 0.0f;

/**
 * @brief 模拟电机速度变化
 */
void simulate_motor_speeds(void)
{
    static uint32_t counter = 0;
    counter++;
    
    // 模拟左电机速度：正弦波变化
    test_left_speed = 50.0f + 30.0f * sin(counter * 0.1f);
    
    // 模拟右电机速度：余弦波变化
    test_right_speed = 45.0f + 25.0f * cos(counter * 0.08f);
    
    // 更新到电机PID实例（如果电机已初始化）
    if (Motor_Left.Motor_PID_Instance.Acutal_Now != 0 || Motor_Right.Motor_PID_Instance.Acutal_Now != 0) {
        Motor_Left.Motor_PID_Instance.Acutal_Now = _IQ(test_left_speed);
        Motor_Right.Motor_PID_Instance.Acutal_Now = _IQ(test_right_speed);
    }
}

/**
 * @brief 测试OLED显示功能
 */
void test_oled_display(void)
{
    // 检查OLED是否可用
    if (!OLED_IsPresent()) {
        MyPrintf_DMA("OLED not present, skipping display test\r\n");
        return;
    }
    
    MyPrintf_DMA("Testing OLED motor speed display...\r\n");
    
    // 清屏
    I2C_OLED_Clear();
    
    // 显示标题
    OLED_ShowString(0, 0, 8, "Motor Speed Test");
    Delay(2000);
    
    // 测试显示格式
    for (int i = 0; i < 10; i++) {
        I2C_OLED_Clear();
        
        // 模拟速度变化
        simulate_motor_speeds();
        
        // 第一排：左电机速度
        OLED_Printf(0, 0, 16, "L Motor: %6.2f", test_left_speed);

        // 第二排：右电机速度
        OLED_Printf(0, 16, 16, "R Motor: %6.2f", test_right_speed);

        // 第三排：预留位置
        // OLED_Printf(0, 32, 16, "Reserved Line 3");

        // 第四排：预留位置
        // OLED_Printf(0, 48, 16, "Reserved Line 4");
        
        MyPrintf_DMA("Test %d: L=%.2f, R=%.2f\r\n", i + 1, test_left_speed, test_right_speed);
        
        Delay(1000); // 每秒更新一次
    }
    
    MyPrintf_DMA("OLED display test completed\r\n");
}

/**
 * @brief 主测试函数
 */
int main_test_oled_motor_display(void)
{
    MyPrintf_DMA("=== OLED Motor Display Test Started ===\r\n");
    
    // 初始化系统
    SYSCFG_DL_init();
    
    // 初始化串口
    Serial_Init();
    
    // 初始化OLED
    bool oled_init_result = OLED_Init();
    MyPrintf_DMA("OLED Init Result: %s\r\n", oled_init_result ? "SUCCESS" : "FAILED");
    
    if (oled_init_result) {
        // 运行显示测试
        test_oled_display();
    } else {
        MyPrintf_DMA("OLED initialization failed, cannot run display test\r\n");
        
        // 尝试诊断I2C问题
        MyPrintf_DMA("Attempting I2C bus recovery...\r\n");
        I2C_OLED_i2c_sda_unlock();
        Delay(100);
        
        // 再次尝试初始化
        oled_init_result = OLED_Init();
        MyPrintf_DMA("OLED Retry Init Result: %s\r\n", oled_init_result ? "SUCCESS" : "FAILED");
        
        if (oled_init_result) {
            test_oled_display();
        }
    }
    
    MyPrintf_DMA("=== OLED Motor Display Test Completed ===\r\n");
    
    return 0;
}
