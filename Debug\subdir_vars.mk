################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Add inputs and outputs from these tool invocations to the build variables 
SYSCFG_SRCS += \
../empty.syscfg 

C_SRCS += \
../OLED_Diagnostic_Tool.c \
./ti_msp_dl_config.c \
C:/ti/mspm0_sdk_2_04_00_06/source/ti/devices/msp/m0p/startup_system_files/ticlang/startup_mspm0g350x_ticlang.c \
../main.c \
../test_init_order.c \
../test_oled_dependency.c \
../test_oled_motor_display.c 

GEN_CMDS += \
./device_linker.cmd 

GEN_FILES += \
./device_linker.cmd \
./device.opt \
./ti_msp_dl_config.c 

C_DEPS += \
./OLED_Diagnostic_Tool.d \
./ti_msp_dl_config.d \
./startup_mspm0g350x_ticlang.d \
./main.d \
./test_init_order.d \
./test_oled_dependency.d \
./test_oled_motor_display.d 

GEN_OPTS += \
./device.opt 

OBJS += \
./OLED_Diagnostic_Tool.o \
./ti_msp_dl_config.o \
./startup_mspm0g350x_ticlang.o \
./main.o \
./test_init_order.o \
./test_oled_dependency.o \
./test_oled_motor_display.o 

GEN_MISC_FILES += \
./device.cmd.genlibs \
./ti_msp_dl_config.h \
./Event.dot 

OBJS__QUOTED += \
"OLED_Diagnostic_Tool.o" \
"ti_msp_dl_config.o" \
"startup_mspm0g350x_ticlang.o" \
"main.o" \
"test_init_order.o" \
"test_oled_dependency.o" \
"test_oled_motor_display.o" 

GEN_MISC_FILES__QUOTED += \
"device.cmd.genlibs" \
"ti_msp_dl_config.h" \
"Event.dot" 

C_DEPS__QUOTED += \
"OLED_Diagnostic_Tool.d" \
"ti_msp_dl_config.d" \
"startup_mspm0g350x_ticlang.d" \
"main.d" \
"test_init_order.d" \
"test_oled_dependency.d" \
"test_oled_motor_display.d" 

GEN_FILES__QUOTED += \
"device_linker.cmd" \
"device.opt" \
"ti_msp_dl_config.c" 

C_SRCS__QUOTED += \
"../OLED_Diagnostic_Tool.c" \
"./ti_msp_dl_config.c" \
"C:/ti/mspm0_sdk_2_04_00_06/source/ti/devices/msp/m0p/startup_system_files/ticlang/startup_mspm0g350x_ticlang.c" \
"../main.c" \
"../test_init_order.c" \
"../test_oled_dependency.c" \
"../test_oled_motor_display.c" 

SYSCFG_SRCS__QUOTED += \
"../empty.syscfg" 


