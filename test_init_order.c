/**
 * @file test_init_order.c
 * @brief 测试系统初始化顺序优化功能
 * @details 验证系统初始化的正确性和模块独立性
 * <AUTHOR> Competition Team
 * @date 2025-01-01
 */

#include "SysConfig.h"

/**
 * @brief 测试核心组件初始化独立性
 * @details 验证核心组件（电机、串口、中断）能够独立于外设模块正常初始化
 */
void Test_Core_Components_Independence(void)
{
    MyPrintf_DMA("=== Testing Core Components Independence ===\r\n");
    
    // 1. 测试电机初始化
    Motor_Start();
    MyPrintf_DMA("Motor initialization: OK\r\n");
    
    // 2. 测试串口初始化
    Serial_Init();
    MyPrintf_DMA("Serial initialization: OK\r\n");
    
    // 3. 测试中断初始化
    Interrupt_Init();
    MyPrintf_DMA("Interrupt initialization: OK\r\n");
    
    // 4. 验证电机能够独立工作
    Motor_Left.Motor_PID_Instance.Target = _IQ(20);
    Motor_Right.Motor_PID_Instance.Target = _IQ(20);
    
    for (int i = 0; i < 100; i++) {
        Motor_GetSpeed(&Motor_Left, 50);
        Motor_GetSpeed(&Motor_Right, 50);
        PID_IQ_Prosc(&Motor_Left.Motor_PID_Instance);
        PID_IQ_Prosc(&Motor_Right.Motor_PID_Instance);
        Motor_SetDuty(&Motor_Left, _IQtoF(Motor_Left.Motor_PID_Instance.Out));
        Motor_SetDuty(&Motor_Right, _IQtoF(Motor_Right.Motor_PID_Instance.Out));
        Delay(50);
    }
    
    MyPrintf_DMA("Core components independence test: PASS\r\n");
}

/**
 * @brief 测试外设模块可选初始化
 * @details 验证外设模块的存在检测和可选初始化功能
 */
void Test_Peripheral_Optional_Init(void)
{
    MyPrintf_DMA("=== Testing Peripheral Optional Initialization ===\r\n");
    
    // 1. 测试OLED模块检测
    bool oled_present = OLED_IsPresent();
    MyPrintf_DMA("OLED detection: %s\r\n", oled_present ? "Present" : "Not Present");
    
    // 2. 测试OLED初始化
    bool oled_init_success = OLED_Init();
    MyPrintf_DMA("OLED initialization: %s\r\n", oled_init_success ? "Success" : "Skipped");
    
    // 3. 测试MPU6050模块检测
    bool mpu6050_present = MPU6050_IsPresent();
    MyPrintf_DMA("MPU6050 detection: %s\r\n", mpu6050_present ? "Present" : "Not Present");
    
    // 4. 测试MPU6050初始化
    bool mpu6050_init_success = MPU6050_Init();
    MyPrintf_DMA("MPU6050 initialization: %s\r\n", mpu6050_init_success ? "Success" : "Skipped");
    
    MyPrintf_DMA("Peripheral optional initialization test: PASS\r\n");
}

/**
 * @brief 测试系统复位功能
 * @details 验证系统软件复位后能够正确重新初始化
 */
void Test_System_Reset_Function(void)
{
    MyPrintf_DMA("=== Testing System Reset Function ===\r\n");
    
    // 1. 记录复位前状态
    uint8_t task_count_before = Task_CheckNum();
    MyPrintf_DMA("Tasks before reset: %d\r\n", task_count_before);
    
    // 2. 设置一些状态
    Motor_Left.Motor_PID_Instance.Target = _IQ(30);
    Motor_Right.Motor_PID_Instance.Target = _IQ(30);
    Data_MotorEncoder[0] = 100;
    Data_MotorEncoder[1] = 100;
    
    // 3. 执行软件复位
    System_SoftReset();
    
    // 4. 验证复位后状态
    uint8_t task_count_after = Task_CheckNum();
    MyPrintf_DMA("Tasks after reset: %d\r\n", task_count_after);
    
    // 5. 验证编码器数据被清零
    if (Data_MotorEncoder[0] == 0 && Data_MotorEncoder[1] == 0) {
        MyPrintf_DMA("Encoder data reset: OK\r\n");
    } else {
        MyPrintf_DMA("Encoder data reset: FAIL\r\n");
    }
    
    // 6. 验证系统能够正常工作
    for (int i = 0; i < 50; i++) {
        Task_Start(Sys_GetTick);
        Delay(20);
    }
    
    MyPrintf_DMA("System reset function test: PASS\r\n");
}

/**
 * @brief 测试初始化顺序的正确性
 * @details 验证初始化顺序符合依赖关系
 */
void Test_Initialization_Order(void)
{
    MyPrintf_DMA("=== Testing Initialization Order ===\r\n");
    
    // 模拟完整的初始化过程
    Task_Init();
    
    // 验证任务是否正确添加
    uint8_t task_count = Task_CheckNum();
    MyPrintf_DMA("Total tasks added: %d\r\n", task_count);
    
    // 运行一段时间验证稳定性
    for (int i = 0; i < 200; i++) {
        Task_Start(Sys_GetTick);
        Delay(10);
    }
    
    MyPrintf_DMA("Initialization order test: PASS\r\n");
}

/**
 * @brief 主测试函数
 */
void Run_Init_Order_Tests(void)
{
    MyPrintf_DMA("=== System Initialization Order Tests ===\r\n");
    
    // 测试1：核心组件独立性
    Test_Core_Components_Independence();
    Delay(500);
    
    // 测试2：外设模块可选初始化
    Test_Peripheral_Optional_Init();
    Delay(500);
    
    // 测试3：初始化顺序正确性
    Test_Initialization_Order();
    Delay(500);
    
    // 测试4：系统复位功能
    Test_System_Reset_Function();
    
    MyPrintf_DMA("=== All Initialization Tests Completed ===\r\n");
}
