******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 17:31:22 2025

OUTPUT FILE NAME:   <TI_CAR1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00007c6d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  0000a348  00015cb8  R  X
  SRAM                  20200000   00008000  0000070c  000078f4  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    0000a348   0000a348    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00008940   00008940    r-x .text
  00008a00    00008a00    000018d0   000018d0    r-- .rodata
  0000a2d0    0000a2d0    00000078   00000078    r-- .cinit
20200000    20200000    0000050d   00000000    rw-
  20200000    20200000    000003d3   00000000    rw- .bss
  202003d4    202003d4    00000139   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00008940     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  00001364    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  0000159c    0000022c     MPU6050.o (.text.Read_Quad)
                  000017c8    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  000019f4    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001c14    000001fc     Task_App.o (.text.Task_Init)
                  00001e10    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00002004    000001e0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000021e4    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  000023c0    000001b0     Task.o (.text.Task_Start)
                  00002570    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  00002710    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000028a2    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000028a4    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00002a2c    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  00002bb4    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002d2c    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002e9c    00000154     MPU6050.o (.text.MPU6050_Init)
                  00002ff0    00000150     OLED.o (.text.OLED_Init)
                  00003140    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  0000327c    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  000033b0    00000134     libc.a : qsort.c.obj (.text.qsort)
                  000034e4    00000130     OLED.o (.text.OLED_ShowChar)
                  00003614    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  00003744    00000128     Task_App.o (.text.Task_Tracker)
                  0000386c    00000128     inv_mpu.o (.text.mpu_init)
                  00003994    00000124     PID_IQMath.o (.text.PID_IQ_Prosc)
                  00003ab8    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  00003bdc    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00003cfc    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003e08    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003f10    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00004014    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00004114    000000f0     Motor.o (.text.Motor_SetDirc)
                  00004204    000000f0     Task_App.o (.text.Task_Motor_PID)
                  000042f4    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  000043e0    000000e4     Interrupt.o (.text.GROUP1_IRQHandler)
                  000044c4    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000045a8    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  0000468c    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00004768    000000dc     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  00004844    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  0000491c    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000049f4    000000d4     inv_mpu.o (.text.set_int_enable)
                  00004ac8    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  00004b98    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  00004c5c    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00004d20    000000bc     Task_App.o (.text.System_SoftReset)
                  00004ddc    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  00004e98    000000b8     Task_App.o (.text.Task_OLED)
                  00004f50    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  00005008    000000b4     Task.o (.text.Task_Add)
                  000050bc    000000ac     Task_App.o (.text.Task_Serial)
                  00005168    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  00005214    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  000052c0    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  0000536a    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  0000536c    000000a2                            : udivmoddi4.S.obj (.text)
                  0000540e    00000002     --HOLE-- [fill = 0]
                  00005410    000000a0     Motor.o (.text.Motor_SetDuty)
                  000054b0    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00005550    0000009c     OLED.o (.text.OLED_IsPresent)
                  000055ec    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  00005688    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  00005720    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  000057b8    00000096     MPU6050.o (.text.inv_row_2_scale)
                  0000584e    00000002     --HOLE-- [fill = 0]
                  00005850    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_Motor_PWM_init)
                  000058dc    0000008c     Task_App.o (.text.Task_Key)
                  00005968    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000059f4    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00005a80    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00005b04    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00005b88    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00005c0a    00000002     --HOLE-- [fill = 0]
                  00005c0c    00000080     Motor.o (.text.Motor_GetSpeed)
                  00005c8c    00000080     Task.o (.text.Task_Resume)
                  00005d0c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00005d88    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00005dfc    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00005e00    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00005e74    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  00005ee8    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00005f5a    00000002     --HOLE-- [fill = 0]
                  00005f5c    00000070     Serial.o (.text.MyPrintf_DMA)
                  00005fcc    0000006e     OLED.o (.text.OLED_ShowString)
                  0000603a    00000002     --HOLE-- [fill = 0]
                  0000603c    0000006c     Motor.o (.text.Motor_Start)
                  000060a8    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00006114    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  0000617e    00000002     --HOLE-- [fill = 0]
                  00006180    00000068     Task.o (.text.Task_Suspend)
                  000061e8    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00006250    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  000062b6    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  0000631c    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00006380    00000064     MPU6050.o (.text.MPU6050_IsPresent)
                  000063e4    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00006448    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000064aa    00000002     --HOLE-- [fill = 0]
                  000064ac    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  0000650e    00000002     --HOLE-- [fill = 0]
                  00006510    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00006570    00000060     Key_Led.o (.text.Key_Read)
                  000065d0    00000060     Task_App.o (.text.Task_IdleFunction)
                  00006630    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00006690    00000060     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  000066f0    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  00006750    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000067ae    00000002     --HOLE-- [fill = 0]
                  000067b0    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  0000680c    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00006868    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  000068c4    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00006920    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00006978    00000058     Serial.o (.text.Serial_Init)
                  000069d0    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00006a28    00000058            : _printfi.c.obj (.text._pconv_f)
                  00006a80    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00006ad6    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00006b28    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  00006b78    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  00006bc8    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00006c18    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  00006c64    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00006cb0    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00006cfc    0000004c     OLED.o (.text.OLED_Printf)
                  00006d48    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00006d94    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00006dde    00000002     --HOLE-- [fill = 0]
                  00006de0    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00006e2a    00000002     --HOLE-- [fill = 0]
                  00006e2c    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00006e74    00000048     ADC.o (.text.adc_getValue)
                  00006ebc    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  00006f04    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  00006f4c    00000048     MPU6050.o (.text.mspm0_i2c_disable)
                  00006f94    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00006fd8    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  0000701c    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  00007060    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  000070a4    00000044     OLED.o (.text.mspm0_i2c_disable)
                  000070e8    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  0000712a    00000002     --HOLE-- [fill = 0]
                  0000712c    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  0000716e    00000002     --HOLE-- [fill = 0]
                  00007170    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  000071b0    00000040     Interrupt.o (.text.Interrupt_Init)
                  000071f0    00000040     Task_App.o (.text.Task_GraySensor)
                  00007230    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00007270    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000072b0    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000072f0    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00007330    0000003e     Task.o (.text.Task_CMP)
                  0000736e    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  000073ac    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000073e8    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00007424    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00007460    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  0000749c    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000074d8    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00007514    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00007550    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  0000758c    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  000075c8    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00007604    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  0000763e    00000002     --HOLE-- [fill = 0]
                  00007640    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  0000767a    00000038     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Normalize_For_User)
                  000076b2    00000002     --HOLE-- [fill = 0]
                  000076b4    00000038     Task_App.o (.text.Task_LED)
                  000076ec    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00007724    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00007758    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0000778c    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000077c0    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  000077f4    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  00007826    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  00007858    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  00007888    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  000078b8    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  000078e8    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00007918    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00007948    00000030            : vsnprintf.c.obj (.text._outs)
                  00007978    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  000079a8    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  000079d8    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00007a04    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00007a30    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00007a5c    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00007a88    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  00007ab2    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00007ada    00000028     OLED.o (.text.DL_Common_updateReg)
                  00007b02    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00007b2a    00000002     --HOLE-- [fill = 0]
                  00007b2c    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00007b54    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00007b7c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00007ba4    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00007bcc    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00007bf4    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00007c1c    00000028     SysTick.o (.text.SysTick_Increasment)
                  00007c44    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00007c6c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00007c94    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00007cba    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  00007ce0    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00007d06    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00007d2c    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00007d50    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00007d74    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00007d98    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00007dba    00000002     --HOLE-- [fill = 0]
                  00007dbc    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00007ddc    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00007dfc    00000020     SysTick.o (.text.Delay)
                  00007e1c    00000020     main.o (.text.main)
                  00007e3c    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  00007e5c    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00007e7a    00000002     --HOLE-- [fill = 0]
                  00007e7c    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00007e9a    00000002     --HOLE-- [fill = 0]
                  00007e9c    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  00007eb8    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  00007ed4    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00007ef0    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00007f0c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00007f28    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00007f44    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00007f60    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00007f7c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00007f98    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00007fb4    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00007fd0    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  00007fec    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00008008    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00008024    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00008040    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  0000805c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00008078    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00008094    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000080b0    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000080cc    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  000080e8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00008100    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00008118    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  00008130    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  00008148    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00008160    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00008178    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00008190    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  000081a8    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  000081c0    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  000081d8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000081f0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00008208    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00008220    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  00008238    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00008250    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00008268    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00008280    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00008298    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000082b0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  000082c8    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  000082e0    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  000082f8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00008310    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00008328    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00008340    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  00008358    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00008370    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00008388    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  000083a0    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  000083b8    00000018     MPU6050.o (.text.DL_I2C_reset)
                  000083d0    00000018     OLED.o (.text.DL_I2C_reset)
                  000083e8    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00008400    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00008418    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00008430    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00008448    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00008460    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00008478    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00008490    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000084a8    00000018     Motor.o (.text.DL_Timer_startCounter)
                  000084c0    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  000084d8    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  000084f0    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00008508    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00008520    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00008538    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00008550    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00008568    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00008580    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  00008598    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  000085b0    00000018            : vsprintf.c.obj (.text._outs)
                  000085c8    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  000085de    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  000085f4    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  0000860a    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00008620    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  00008636    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  0000864c    00000016     OLED.o (.text.DL_GPIO_readPins)
                  00008662    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00008678    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  0000868e    00000016     SysTick.o (.text.SysGetTick)
                  000086a4    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000086ba    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  000086ce    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  000086e2    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  000086f6    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  0000870a    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  0000871e    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00008732    00000002     --HOLE-- [fill = 0]
                  00008734    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00008748    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  0000875c    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00008770    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00008784    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00008798    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000087ac    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000087c0    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000087d4    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  000087e8    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  000087fc    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00008810    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00008822    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00008834    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00008846    00000010     ADC.o (.text.DL_ADC12_getStatus)
                  00008856    00000002     --HOLE-- [fill = 0]
                  00008858    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00008868    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00008878    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00008888    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00008898    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  000088a6    00000002     --HOLE-- [fill = 0]
                  000088a8    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  000088b6    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000088c4    0000000e     MPU6050.o (.text.tap_cb)
                  000088d2    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  000088e0    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  000088ec    0000000c     SysTick.o (.text.Sys_GetTick)
                  000088f8    0000000c     Task.o (.text.Task_CheckNum)
                  00008904    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000890e    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00008918    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00008928    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00008932    00000002     --HOLE-- [fill = 0]
                  00008934    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00008944    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000894e    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00008958    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00008962    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  0000896c    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  0000897c    0000000a     libc.a : vsprintf.c.obj (.text._outc)
                  00008986    0000000a     MPU6050.o (.text.android_orient_cb)
                  00008990    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00008998    00000008     Interrupt.o (.text.SysTick_Handler)
                  000089a0    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000089a8    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000089b0    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  000089b6    00000002     --HOLE-- [fill = 0]
                  000089b8    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  000089c8    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  000089ce    00000006            : exit.c.obj (.text:abort)
                  000089d4    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000089d8    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  000089dc    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000089e0    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  000089f0    00000004            : pre_init.c.obj (.text._system_pre_init)
                  000089f4    0000000c     --HOLE-- [fill = 0]

.cinit     0    0000a2d0    00000078     
                  0000a2d0    00000050     (.cinit..data.load) [load image, compression = lzss]
                  0000a320    0000000c     (__TI_handler_table)
                  0000a32c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  0000a334    00000010     (__TI_cinit_table)
                  0000a344    00000004     --HOLE-- [fill = 0]

.rodata    0    00008a00    000018d0     
                  00008a00    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  000095f6    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00009be6    00000228     OLED_Font.o (.rodata.asc2_0806)
                  00009e0e    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00009e10    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00009f11    00000007     Task_App.o (.rodata.str1.11952760121962574671.1)
                  00009f18    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00009f58    00000032     Task_App.o (.rodata.str1.16389650136473743432.1)
                  00009f8a    00000029     Task_App.o (.rodata.str1.4133793139133961309.1)
                  00009fb3    00000001     --HOLE-- [fill = 0]
                  00009fb4    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00009fdc    00000028     inv_mpu.o (.rodata.test)
                  0000a004    00000027     Task_App.o (.rodata.str1.7950429023856218820.1)
                  0000a02b    00000025     Task_App.o (.rodata.str1.5297265082290894444.1)
                  0000a050    00000024     Task_App.o (.rodata.str1.1200745476254391468.1)
                  0000a074    00000022     Task_App.o (.rodata.str1.5161480910995489644.1)
                  0000a096    00000020     Task_App.o (.rodata.str1.8896853068034818020.1)
                  0000a0b6    0000001f     Task_App.o (.rodata.str1.3403810636401035402.1)
                  0000a0d5    0000001f     Task_App.o (.rodata.str1.3850258909703972507.1)
                  0000a0f4    0000001e     inv_mpu.o (.rodata.reg)
                  0000a112    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  0000a114    0000001c     Task_App.o (.rodata..L__const.System_SoftReset.task_names)
                  0000a130    0000001a     Task_App.o (.rodata.str1.12629676409056169537.1)
                  0000a14a    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  0000a14c    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  0000a164    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  0000a17c    00000018     Task_App.o (.rodata.str1.10635198597896025474.1)
                  0000a194    00000017     Task_App.o (.rodata.str1.3743034515018940988.1)
                  0000a1ab    00000014     OLED.o (.rodata.str1.10627719544674466389.1)
                  0000a1bf    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  0000a1d0    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  0000a1e1    00000011     Task_App.o (.rodata.str1.6643214035752360150.1)
                  0000a1f2    00000011     Task_App.o (.rodata.str1.7346008805793267871.1)
                  0000a203    00000010     Task_App.o (.rodata.str1.11373919790952722939.1)
                  0000a213    0000000f     Task_App.o (.rodata.str1.4374607466655451358.1)
                  0000a222    0000000f     Task_App.o (.rodata.str1.7269909886346255147.1)
                  0000a231    0000000e     Task_App.o (.rodata.str1.5017135634981511656.1)
                  0000a23f    0000000d     Task_App.o (.rodata.str1.13861004553356644102.1)
                  0000a24c    0000000c     inv_mpu.o (.rodata.hw)
                  0000a258    0000000c     Task_App.o (.rodata.str1.12980382611605970010.1)
                  0000a264    0000000c     Task_App.o (.rodata.str1.13166305789289702848.1)
                  0000a270    0000000c     OLED.o (.rodata.str1.475677112691578884.1)
                  0000a27c    0000000b     Task_App.o (.rodata.str1.4769078833470683459.1)
                  0000a287    0000000b     Task_App.o (.rodata.str1.9092480108036065651.1)
                  0000a292    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  0000a29c    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  0000a2a4    00000008     ti_msp_dl_config.o (.rodata.gMotor_PWMConfig)
                  0000a2ac    00000008     Task_App.o (.rodata.str1.11683036942922059812.1)
                  0000a2b4    00000006     Task_App.o (.rodata.str1.16020955549137178199.1)
                  0000a2ba    00000005     Task_App.o (.rodata.str1.5883415095785080416.1)
                  0000a2bf    00000004     Task_App.o (.rodata.str1.14074990341397557290.1)
                  0000a2c3    00000004     Task_App.o (.rodata.str1.492715258893803702.1)
                  0000a2c7    00000003     ti_msp_dl_config.o (.rodata.gMotor_PWMClockConfig)
                  0000a2ca    00000003     Task_App.o (.rodata.str1.16301319874972139807.1)
                  0000a2cd    00000003     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000003d3     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000b0     (.common:GraySensor)
                  202003a0    00000010     (.common:quat)
                  202003b0    00000006     (.common:Data_Accel)
                  202003b6    00000006     (.common:Data_Gyro)
                  202003bc    00000004     (.common:Data_Pitch)
                  202003c0    00000004     (.common:Data_Roll)
                  202003c4    00000004     (.common:Data_Yaw)
                  202003c8    00000004     (.common:ExISR_Flag)
                  202003cc    00000004     (.common:sensor_timestamp)
                  202003d0    00000002     (.common:sensors)
                  202003d2    00000001     (.common:more)

.data      0    202003d4    00000139     UNINITIALIZED
                  202003d4    00000048     Motor.o (.data.Motor_Left)
                  2020041c    00000048     Motor.o (.data.Motor_Right)
                  20200464    0000002c     inv_mpu.o (.data.st)
                  20200490    00000010     Task_App.o (.data.Gray_Anolog)
                  202004a0    00000010     Task_App.o (.data.Gray_Normal)
                  202004b0    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  202004c0    0000000e     MPU6050.o (.data.hal)
                  202004ce    00000009     MPU6050.o (.data.gyro_orientation)
                  202004d7    00000008     Task_App.o (.data.Data_Tracker_Input)
                  202004df    00000001     Task_App.o (.data.Flag_LED)
                  202004e0    00000008     Task_App.o (.data.Motor)
                  202004e8    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202004ec    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202004f0    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202004f4    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004f8    00000004     SysTick.o (.data.delayTick)
                  202004fc    00000004     SysTick.o (.data.uwTick)
                  20200500    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  20200502    00000002     Task_App.o (.data.Task_Key.Key_Press_Count)
                  20200504    00000002     Task_App.o (.data.Task_OLED.oled_error_count)
                  20200506    00000002     Task_App.o (.data.Task_OLED.oled_reinit_timer)
                  20200508    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  20200509    00000001     Task_App.o (.data.Gray_Digtal)
                  2020050a    00000001     Task_App.o (.data.Task_Key.Key_Old)
                  2020050b    00000001     Task.o (.data.Task_Num)
                  2020050c    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               3426    123       0      
       startup_mspm0g350x_ticlang.o     8       192       0      
       main.o                           32      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3466    315       0      
                                                                 
    .\APP\Src\
       Task_App.o                       1988    622       247    
       Interrupt.o                      470     0         6      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2458    622       253    
                                                                 
    .\BSP\Src\
       MPU6050.o                        2584    0         70     
       OLED.o                           2074    32        0      
       OLED_Font.o                      0       2072      0      
       No_Mcu_Ganv_Grayscale_Sensor.o   1244    0         0      
       Task.o                           918     0         241    
       Serial.o                         404     0         512    
       Motor.o                          704     0         144    
       PID_IQMath.o                     402     0         0      
       ADC.o                            236     0         0      
       Key_Led.o                        118     0         0      
       SysTick.o                        106     0         8      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8790    2104      975    
                                                                 
    .\DMP\
       inv_mpu_dmp_motion_driver.o      3110    3062      16     
       inv_mpu.o                        4600    82        44     
    +--+--------------------------------+-------+---------+---------+
       Total:                           7710    3144      60     
                                                                 
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o     388     0         0      
       dl_timer.o                       356     0         0      
       dl_i2c.o                         192     0         0      
       dl_uart.o                        90      0         0      
       dl_dma.o                         76      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1176    0         0      
                                                                 
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/iqmath/lib/ticlang/m0p/mathacl/mspm0g1x0x_g3x0x/iqmath.a
       _IQNtoF.o                        48      0         0      
       _IQNdiv.o                        24      0         0      
       _IQNmpy.o                        24      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           96      0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   4510    34        0      
       e_asin.c.obj                     908     0         0      
       s_atan.c.obj                     784     64        0      
       e_atan2.c.obj                    392     0         0      
       e_sqrt.c.obj                     368     0         0      
       qsort.c.obj                      308     0         0      
       aeabi_ctype.S.obj                0       257       0      
       s_scalbn.c.obj                   216     0         0      
       vsnprintf.c.obj                  136     0         0      
       copy_decompress_lzss.c.obj       124     0         0      
       s_frexp.c.obj                    92      0         0      
       _ltoa.c.obj                      88      0         0      
       vsprintf.c.obj                   78      0         0      
       atoi.c.obj                       64      0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       memccpy.c.obj                    34      0         0      
       memcmp.c.obj                     32      0         0      
       copy_zero_init.c.obj             22      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       wcslen.c.obj                     16      0         0      
       memset16.S.obj                   14      0         0      
       aeabi_portable.c.obj             8       0         4      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8356    355       4      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     434     0         0      
       divdf3.S.obj                     284     0         0      
       muldf3.S.obj                     244     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       udivmoddi4.S.obj                 162     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       truncdfsf2.S.obj                 116     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       aeabi_idivmod.S.obj              86      0         0      
       fixdfsi.S.obj                    74      0         0      
       fixunsdfsi.S.obj                 66      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       extendsfdf2.S.obj                64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       muldi3.S.obj                     36      0         0      
       ashldi3.S.obj                    30      0         0      
       aeabi_uldivmod.S.obj             20      0         0      
       aeabi_memset.S.obj               14      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3020    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       116       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     35076   6656      1804   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 0000a334 records: 2, size/record: 8, table size: 16
	.data: load addr=0000a2d0, load size=00000050 bytes, run addr=202003d4, run size=00000139 bytes, compression=lzss
	.bss: load addr=0000a32c, load size=00000008 bytes, run addr=20200000, run size=000003d3 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 0000a320 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00002711     00008918     00008916   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   000044c5     00008934     00008930   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             0000894c          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00008960          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00008996          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             000089cc          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00003cfd     0000896c     0000896a   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   0000271b     000089b8     000089b4   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             000089da          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00007c6d     000089e0     000089dc   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00005dfd  ADC0_IRQHandler                      
00005dfd  ADC1_IRQHandler                      
00005dfd  AES_IRQHandler                       
000089d4  C$$EXIT                              
00005dfd  CANFD0_IRQHandler                    
00005dfd  DAC0_IRQHandler                      
00007171  DL_ADC12_setClockConfig              
00008905  DL_Common_delayCycles                
00006c65  DL_DMA_initChannel                   
00006751  DL_I2C_fillControllerTXFIFO          
00007461  DL_I2C_flushControllerTXFIFO         
00007d07  DL_I2C_setClockConfig                
0000468d  DL_SYSCTL_configSYSPLL               
0000631d  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006f95  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003f11  DL_Timer_initFourCCPWMMode           
00008079  DL_Timer_setCaptCompUpdateMethod     
00008491  DL_Timer_setCaptureCompareOutCtl     
00008869  DL_Timer_setCaptureCompareValue      
00008095  DL_Timer_setClockConfig              
00006e2d  DL_UART_init                         
00008811  DL_UART_setClockConfig               
00005dfd  DMA_IRQHandler                       
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202004e8  Data_MotorEncoder                    
202004ec  Data_Motor_TarSpeed                  
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202004d7  Data_Tracker_Input                   
202004f0  Data_Tracker_Offset                  
202003c4  Data_Yaw                             
00005dfd  Default_Handler                      
00007dfd  Delay                                
202003c8  ExISR_Flag                           
202004df  Flag_LED                             
20200508  Flag_MPU6050_Ready                   
00005dfd  GROUP0_IRQHandler                    
000043e1  GROUP1_IRQHandler                    
00004769  Get_Analog_value                     
000074d9  Get_Anolog_Value                     
00008899  Get_Digtal_For_User                  
0000767b  Get_Normalize_For_User               
202002f0  GraySensor                           
20200490  Gray_Anolog                          
20200509  Gray_Digtal                          
202004a0  Gray_Normal                          
000089d5  HOSTexit                             
00005dfd  HardFault_Handler                    
00005dfd  I2C0_IRQHandler                      
00005dfd  I2C1_IRQHandler                      
00006115  I2C_OLED_Clear                       
00007515  I2C_OLED_Set_Pos                     
00005689  I2C_OLED_WR_Byte                     
00006511  I2C_OLED_i2c_sda_unlock              
000071b1  Interrupt_Init                       
00006571  Key_Read                             
00002e9d  MPU6050_Init                         
00006381  MPU6050_IsPresent                    
202004e0  Motor                                
00005c0d  Motor_GetSpeed                       
202003d4  Motor_Left                           
2020041c  Motor_Right                          
00005411  Motor_SetDuty                        
0000603d  Motor_Start                          
00005f5d  MyPrintf_DMA                         
00005dfd  NMI_Handler                          
000028a5  No_MCU_Ganv_Sensor_Init              
00005ee9  No_MCU_Ganv_Sensor_Init_Frist        
000070e9  No_Mcu_Ganv_Sensor_Task_Without_tick 
00002ff1  OLED_Init                            
00005551  OLED_IsPresent                       
00006cfd  OLED_Printf                          
000034e5  OLED_ShowChar                        
00005fcd  OLED_ShowString                      
00007a89  PID_IQ_Init                          
00003995  PID_IQ_Prosc                         
00006fd9  PID_IQ_SetParams                     
00005dfd  PendSV_Handler                       
00005dfd  RTC_IRQHandler                       
0000159d  Read_Quad                            
000089dd  Reset_Handler                        
00005dfd  SPI0_IRQHandler                      
00005dfd  SPI1_IRQHandler                      
00005dfd  SVC_Handler                          
00006d49  SYSCFG_DL_ADC1_init                  
000078b9  SYSCFG_DL_DMA_CH_RX_init             
00008551  SYSCFG_DL_DMA_CH_TX_init             
000088e1  SYSCFG_DL_DMA_init                   
00002005  SYSCFG_DL_GPIO_init                  
00006921  SYSCFG_DL_I2C_MPU6050_init           
000063e5  SYSCFG_DL_I2C_OLED_init              
00005851  SYSCFG_DL_Motor_PWM_init             
000067b1  SYSCFG_DL_SYSCTL_init                
00008879  SYSCFG_DL_SYSTICK_init               
00005a81  SYSCFG_DL_UART0_init                 
000079d9  SYSCFG_DL_init                       
000054b1  SYSCFG_DL_initPower                  
00006979  Serial_Init                          
20200000  Serial_RxData                        
0000868f  SysGetTick                           
00008999  SysTick_Handler                      
00007c1d  SysTick_Increasment                  
000088ed  Sys_GetTick                          
00004d21  System_SoftReset                     
00005dfd  TIMA0_IRQHandler                     
00005dfd  TIMA1_IRQHandler                     
00005dfd  TIMG0_IRQHandler                     
00005dfd  TIMG12_IRQHandler                    
00005dfd  TIMG6_IRQHandler                     
00005dfd  TIMG7_IRQHandler                     
00005dfd  TIMG8_IRQHandler                     
00008823  TI_memcpy_small                      
000088d3  TI_memset_small                      
00005009  Task_Add                             
000088f9  Task_CheckNum                        
000071f1  Task_GraySensor                      
000065d1  Task_IdleFunction                    
00001c15  Task_Init                            
000058dd  Task_Key                             
000076b5  Task_LED                             
00004205  Task_Motor_PID                       
00004e99  Task_OLED                            
00005c8d  Task_Resume                          
000050bd  Task_Serial                          
000023c1  Task_Start                           
00006181  Task_Suspend                         
00003745  Task_Tracker                         
00005dfd  UART0_IRQHandler                     
00005dfd  UART1_IRQHandler                     
00005dfd  UART2_IRQHandler                     
00005dfd  UART3_IRQHandler                     
00008569  _IQ24div                             
00008581  _IQ24mpy                             
000078e9  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
0000a334  __TI_CINIT_Base                      
0000a344  __TI_CINIT_Limit                     
0000a344  __TI_CINIT_Warm                      
0000a320  __TI_Handler_Table_Base              
0000a32c  __TI_Handler_Table_Limit             
000075c9  __TI_auto_init_nobinit_nopinit       
00005d0d  __TI_decompress_lzss                 
00008835  __TI_decompress_none                 
000069d1  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000086a5  __TI_zero_init_nomemset              
0000271b  __adddf3                             
00004927  __addsf3                             
00009e10  __aeabi_ctype_table_                 
00009e10  __aeabi_ctype_table_C                
00005e01  __aeabi_d2f                          
00006de1  __aeabi_d2iz                         
0000712d  __aeabi_d2uiz                        
0000271b  __aeabi_dadd                         
00006449  __aeabi_dcmpeq                       
00006485  __aeabi_dcmpge                       
00006499  __aeabi_dcmpgt                       
00006471  __aeabi_dcmple                       
0000645d  __aeabi_dcmplt                       
00003cfd  __aeabi_ddiv                         
000044c5  __aeabi_dmul                         
00002711  __aeabi_dsub                         
202004f4  __aeabi_errno                        
000089a1  __aeabi_errno_addr                   
00007271  __aeabi_f2d                          
000076ed  __aeabi_f2iz                         
00004927  __aeabi_fadd                         
000064ad  __aeabi_fcmpeq                       
000064e9  __aeabi_fcmpge                       
000064fd  __aeabi_fcmpgt                       
000064d5  __aeabi_fcmple                       
000064c1  __aeabi_fcmplt                       
00005b89  __aeabi_fdiv                         
00005969  __aeabi_fmul                         
0000491d  __aeabi_fsub                         
00007a31  __aeabi_i2d                          
00007551  __aeabi_i2f                          
00006a81  __aeabi_idiv                         
000028a3  __aeabi_idiv0                        
00006a81  __aeabi_idivmod                      
0000536b  __aeabi_ldiv0                        
00007e7d  __aeabi_llsl                         
00007d75  __aeabi_lmul                         
000089a9  __aeabi_memcpy                       
000089a9  __aeabi_memcpy4                      
000089a9  __aeabi_memcpy8                      
000088a9  __aeabi_memset                       
000088a9  __aeabi_memset4                      
000088a9  __aeabi_memset8                      
00007d51  __aeabi_ui2d                         
00007c45  __aeabi_ui2f                         
00007231  __aeabi_uidiv                        
00007231  __aeabi_uidivmod                     
000087c1  __aeabi_uldivmod                     
00007e7d  __ashldi3                            
ffffffff  __binit__                            
000061e9  __cmpdf2                             
00007605  __cmpsf2                             
00003cfd  __divdf3                             
00005b89  __divsf3                             
000061e9  __eqdf2                              
00007605  __eqsf2                              
00007271  __extendsfdf2                        
00006de1  __fixdfsi                            
000076ed  __fixsfsi                            
0000712d  __fixunsdfsi                         
00007a31  __floatsidf                          
00007551  __floatsisf                          
00007d51  __floatunsidf                        
00007c45  __floatunsisf                        
00005d89  __gedf2                              
0000758d  __gesf2                              
00005d89  __gtdf2                              
0000758d  __gtsf2                              
000061e9  __ledf2                              
00007605  __lesf2                              
000061e9  __ltdf2                              
00007605  __ltsf2                              
UNDEFED   __mpu_init                           
000044c5  __muldf3                             
00007d75  __muldi3                             
00007641  __muldsi3                            
00005969  __mulsf3                             
000061e9  __nedf2                              
00007605  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00002711  __subdf3                             
0000491d  __subsf3                             
00005e01  __truncdfsf2                         
0000536d  __udivmoddi4                         
00007c6d  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
000089f1  _system_pre_init                     
000089cf  abort                                
00006e75  adc_getValue                         
00009be6  asc2_0806                            
000095f6  asc2_1608                            
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00002a2d  atan2                                
00002a2d  atan2l                               
00000df5  atanl                                
000072b1  atoi                                 
ffffffff  binit                                
000060a9  convertAnalogToDigital               
202004f8  delayTick                            
00006ebd  dmp_enable_6x_lp_quat                
000010ed  dmp_enable_feature                   
00006631  dmp_enable_gyro_cal                  
00006f05  dmp_enable_lp_quat                   
000080cd  dmp_load_motion_driver_firmware      
00001e11  dmp_read_fifo                        
000087d5  dmp_register_android_orient_cb       
000087e9  dmp_register_tap_cb                  
00005721  dmp_set_fifo_rate                    
00002bb5  dmp_set_orientation                  
0000701d  dmp_set_shake_reject_thresh          
000077f5  dmp_set_shake_reject_time            
00007827  dmp_set_shake_reject_timeout         
000062b7  dmp_set_tap_axes                     
00007061  dmp_set_tap_count                    
00001365  dmp_set_tap_thresh                   
00007979  dmp_set_tap_time                     
000079a9  dmp_set_tap_time_multi               
2020050c  enable_group1_irq                    
0000680d  frexp                                
0000680d  frexpl                               
0000a24c  hw                                   
00000000  interruptVectors                     
00004845  ldexp                                
00004845  ldexpl                               
00007e1d  main                                 
00007d99  memccpy                              
00007e3d  memcmp                               
202003d2  more                                 
00006691  mpu6050_i2c_sda_unlock               
00004ddd  mpu_configure_fifo                   
00005e75  mpu_get_accel_fsr                    
000066f1  mpu_get_gyro_fsr                     
000077c1  mpu_get_sample_rate                  
0000386d  mpu_init                             
00003ab9  mpu_load_firmware                    
00004015  mpu_lp_accel_mode                    
00003e09  mpu_read_fifo_stream                 
00005169  mpu_read_mem                         
000017c9  mpu_reset_fifo                       
000045a9  mpu_set_accel_fsr                    
00002571  mpu_set_bypass                       
00004f51  mpu_set_dmp_state                    
00004b99  mpu_set_gyro_fsr                     
000055ed  mpu_set_int_latched                  
00004ac9  mpu_set_lpf                          
000042f5  mpu_set_sample_rate                  
00003615  mpu_set_sensors                      
00005215  mpu_write_mem                        
0000327d  mspm0_i2c_read                       
00004c5d  mspm0_i2c_write                      
000052c1  normalizeAnalogValues                
000033b1  qsort                                
202003a0  quat                                 
0000a0f4  reg                                  
00004845  scalbn                               
00004845  scalbnl                              
202003cc  sensor_timestamp                     
202003d0  sensors                              
00002d2d  sqrt                                 
00002d2d  sqrtl                                
00009fdc  test                                 
202004fc  uwTick                               
000072f1  vsnprintf                            
00007a5d  vsprintf                             
00008889  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  dmp_enable_feature                   
00001365  dmp_set_tap_thresh                   
0000159d  Read_Quad                            
000017c9  mpu_reset_fifo                       
00001c15  Task_Init                            
00001e11  dmp_read_fifo                        
00002005  SYSCFG_DL_GPIO_init                  
000023c1  Task_Start                           
00002571  mpu_set_bypass                       
00002711  __aeabi_dsub                         
00002711  __subdf3                             
0000271b  __adddf3                             
0000271b  __aeabi_dadd                         
000028a3  __aeabi_idiv0                        
000028a5  No_MCU_Ganv_Sensor_Init              
00002a2d  atan2                                
00002a2d  atan2l                               
00002bb5  dmp_set_orientation                  
00002d2d  sqrt                                 
00002d2d  sqrtl                                
00002e9d  MPU6050_Init                         
00002ff1  OLED_Init                            
0000327d  mspm0_i2c_read                       
000033b1  qsort                                
000034e5  OLED_ShowChar                        
00003615  mpu_set_sensors                      
00003745  Task_Tracker                         
0000386d  mpu_init                             
00003995  PID_IQ_Prosc                         
00003ab9  mpu_load_firmware                    
00003cfd  __aeabi_ddiv                         
00003cfd  __divdf3                             
00003e09  mpu_read_fifo_stream                 
00003f11  DL_Timer_initFourCCPWMMode           
00004015  mpu_lp_accel_mode                    
00004205  Task_Motor_PID                       
000042f5  mpu_set_sample_rate                  
000043e1  GROUP1_IRQHandler                    
000044c5  __aeabi_dmul                         
000044c5  __muldf3                             
000045a9  mpu_set_accel_fsr                    
0000468d  DL_SYSCTL_configSYSPLL               
00004769  Get_Analog_value                     
00004845  ldexp                                
00004845  ldexpl                               
00004845  scalbn                               
00004845  scalbnl                              
0000491d  __aeabi_fsub                         
0000491d  __subsf3                             
00004927  __addsf3                             
00004927  __aeabi_fadd                         
00004ac9  mpu_set_lpf                          
00004b99  mpu_set_gyro_fsr                     
00004c5d  mspm0_i2c_write                      
00004d21  System_SoftReset                     
00004ddd  mpu_configure_fifo                   
00004e99  Task_OLED                            
00004f51  mpu_set_dmp_state                    
00005009  Task_Add                             
000050bd  Task_Serial                          
00005169  mpu_read_mem                         
00005215  mpu_write_mem                        
000052c1  normalizeAnalogValues                
0000536b  __aeabi_ldiv0                        
0000536d  __udivmoddi4                         
00005411  Motor_SetDuty                        
000054b1  SYSCFG_DL_initPower                  
00005551  OLED_IsPresent                       
000055ed  mpu_set_int_latched                  
00005689  I2C_OLED_WR_Byte                     
00005721  dmp_set_fifo_rate                    
00005851  SYSCFG_DL_Motor_PWM_init             
000058dd  Task_Key                             
00005969  __aeabi_fmul                         
00005969  __mulsf3                             
00005a81  SYSCFG_DL_UART0_init                 
00005b89  __aeabi_fdiv                         
00005b89  __divsf3                             
00005c0d  Motor_GetSpeed                       
00005c8d  Task_Resume                          
00005d0d  __TI_decompress_lzss                 
00005d89  __gedf2                              
00005d89  __gtdf2                              
00005dfd  ADC0_IRQHandler                      
00005dfd  ADC1_IRQHandler                      
00005dfd  AES_IRQHandler                       
00005dfd  CANFD0_IRQHandler                    
00005dfd  DAC0_IRQHandler                      
00005dfd  DMA_IRQHandler                       
00005dfd  Default_Handler                      
00005dfd  GROUP0_IRQHandler                    
00005dfd  HardFault_Handler                    
00005dfd  I2C0_IRQHandler                      
00005dfd  I2C1_IRQHandler                      
00005dfd  NMI_Handler                          
00005dfd  PendSV_Handler                       
00005dfd  RTC_IRQHandler                       
00005dfd  SPI0_IRQHandler                      
00005dfd  SPI1_IRQHandler                      
00005dfd  SVC_Handler                          
00005dfd  TIMA0_IRQHandler                     
00005dfd  TIMA1_IRQHandler                     
00005dfd  TIMG0_IRQHandler                     
00005dfd  TIMG12_IRQHandler                    
00005dfd  TIMG6_IRQHandler                     
00005dfd  TIMG7_IRQHandler                     
00005dfd  TIMG8_IRQHandler                     
00005dfd  UART0_IRQHandler                     
00005dfd  UART1_IRQHandler                     
00005dfd  UART2_IRQHandler                     
00005dfd  UART3_IRQHandler                     
00005e01  __aeabi_d2f                          
00005e01  __truncdfsf2                         
00005e75  mpu_get_accel_fsr                    
00005ee9  No_MCU_Ganv_Sensor_Init_Frist        
00005f5d  MyPrintf_DMA                         
00005fcd  OLED_ShowString                      
0000603d  Motor_Start                          
000060a9  convertAnalogToDigital               
00006115  I2C_OLED_Clear                       
00006181  Task_Suspend                         
000061e9  __cmpdf2                             
000061e9  __eqdf2                              
000061e9  __ledf2                              
000061e9  __ltdf2                              
000061e9  __nedf2                              
000062b7  dmp_set_tap_axes                     
0000631d  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006381  MPU6050_IsPresent                    
000063e5  SYSCFG_DL_I2C_OLED_init              
00006449  __aeabi_dcmpeq                       
0000645d  __aeabi_dcmplt                       
00006471  __aeabi_dcmple                       
00006485  __aeabi_dcmpge                       
00006499  __aeabi_dcmpgt                       
000064ad  __aeabi_fcmpeq                       
000064c1  __aeabi_fcmplt                       
000064d5  __aeabi_fcmple                       
000064e9  __aeabi_fcmpge                       
000064fd  __aeabi_fcmpgt                       
00006511  I2C_OLED_i2c_sda_unlock              
00006571  Key_Read                             
000065d1  Task_IdleFunction                    
00006631  dmp_enable_gyro_cal                  
00006691  mpu6050_i2c_sda_unlock               
000066f1  mpu_get_gyro_fsr                     
00006751  DL_I2C_fillControllerTXFIFO          
000067b1  SYSCFG_DL_SYSCTL_init                
0000680d  frexp                                
0000680d  frexpl                               
00006921  SYSCFG_DL_I2C_MPU6050_init           
00006979  Serial_Init                          
000069d1  __TI_ltoa                            
00006a81  __aeabi_idiv                         
00006a81  __aeabi_idivmod                      
00006c65  DL_DMA_initChannel                   
00006cfd  OLED_Printf                          
00006d49  SYSCFG_DL_ADC1_init                  
00006de1  __aeabi_d2iz                         
00006de1  __fixdfsi                            
00006e2d  DL_UART_init                         
00006e75  adc_getValue                         
00006ebd  dmp_enable_6x_lp_quat                
00006f05  dmp_enable_lp_quat                   
00006f95  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00006fd9  PID_IQ_SetParams                     
0000701d  dmp_set_shake_reject_thresh          
00007061  dmp_set_tap_count                    
000070e9  No_Mcu_Ganv_Sensor_Task_Without_tick 
0000712d  __aeabi_d2uiz                        
0000712d  __fixunsdfsi                         
00007171  DL_ADC12_setClockConfig              
000071b1  Interrupt_Init                       
000071f1  Task_GraySensor                      
00007231  __aeabi_uidiv                        
00007231  __aeabi_uidivmod                     
00007271  __aeabi_f2d                          
00007271  __extendsfdf2                        
000072b1  atoi                                 
000072f1  vsnprintf                            
00007461  DL_I2C_flushControllerTXFIFO         
000074d9  Get_Anolog_Value                     
00007515  I2C_OLED_Set_Pos                     
00007551  __aeabi_i2f                          
00007551  __floatsisf                          
0000758d  __gesf2                              
0000758d  __gtsf2                              
000075c9  __TI_auto_init_nobinit_nopinit       
00007605  __cmpsf2                             
00007605  __eqsf2                              
00007605  __lesf2                              
00007605  __ltsf2                              
00007605  __nesf2                              
00007641  __muldsi3                            
0000767b  Get_Normalize_For_User               
000076b5  Task_LED                             
000076ed  __aeabi_f2iz                         
000076ed  __fixsfsi                            
000077c1  mpu_get_sample_rate                  
000077f5  dmp_set_shake_reject_time            
00007827  dmp_set_shake_reject_timeout         
000078b9  SYSCFG_DL_DMA_CH_RX_init             
000078e9  _IQ24toF                             
00007979  dmp_set_tap_time                     
000079a9  dmp_set_tap_time_multi               
000079d9  SYSCFG_DL_init                       
00007a31  __aeabi_i2d                          
00007a31  __floatsidf                          
00007a5d  vsprintf                             
00007a89  PID_IQ_Init                          
00007c1d  SysTick_Increasment                  
00007c45  __aeabi_ui2f                         
00007c45  __floatunsisf                        
00007c6d  _c_int00_noargs                      
00007d07  DL_I2C_setClockConfig                
00007d51  __aeabi_ui2d                         
00007d51  __floatunsidf                        
00007d75  __aeabi_lmul                         
00007d75  __muldi3                             
00007d99  memccpy                              
00007dfd  Delay                                
00007e1d  main                                 
00007e3d  memcmp                               
00007e7d  __aeabi_llsl                         
00007e7d  __ashldi3                            
00008079  DL_Timer_setCaptCompUpdateMethod     
00008095  DL_Timer_setClockConfig              
000080cd  dmp_load_motion_driver_firmware      
00008491  DL_Timer_setCaptureCompareOutCtl     
00008551  SYSCFG_DL_DMA_CH_TX_init             
00008569  _IQ24div                             
00008581  _IQ24mpy                             
0000868f  SysGetTick                           
000086a5  __TI_zero_init_nomemset              
000087c1  __aeabi_uldivmod                     
000087d5  dmp_register_android_orient_cb       
000087e9  dmp_register_tap_cb                  
00008811  DL_UART_setClockConfig               
00008823  TI_memcpy_small                      
00008835  __TI_decompress_none                 
00008869  DL_Timer_setCaptureCompareValue      
00008879  SYSCFG_DL_SYSTICK_init               
00008889  wcslen                               
00008899  Get_Digtal_For_User                  
000088a9  __aeabi_memset                       
000088a9  __aeabi_memset4                      
000088a9  __aeabi_memset8                      
000088d3  TI_memset_small                      
000088e1  SYSCFG_DL_DMA_init                   
000088ed  Sys_GetTick                          
000088f9  Task_CheckNum                        
00008905  DL_Common_delayCycles                
00008999  SysTick_Handler                      
000089a1  __aeabi_errno_addr                   
000089a9  __aeabi_memcpy                       
000089a9  __aeabi_memcpy4                      
000089a9  __aeabi_memcpy8                      
000089cf  abort                                
000089d4  C$$EXIT                              
000089d5  HOSTexit                             
000089dd  Reset_Handler                        
000089f1  _system_pre_init                     
000095f6  asc2_1608                            
00009be6  asc2_0806                            
00009e10  __aeabi_ctype_table_                 
00009e10  __aeabi_ctype_table_C                
00009fdc  test                                 
0000a0f4  reg                                  
0000a24c  hw                                   
0000a320  __TI_Handler_Table_Base              
0000a32c  __TI_Handler_Table_Limit             
0000a334  __TI_CINIT_Base                      
0000a344  __TI_CINIT_Limit                     
0000a344  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  GraySensor                           
202003a0  quat                                 
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202003c4  Data_Yaw                             
202003c8  ExISR_Flag                           
202003cc  sensor_timestamp                     
202003d0  sensors                              
202003d2  more                                 
202003d4  Motor_Left                           
2020041c  Motor_Right                          
20200490  Gray_Anolog                          
202004a0  Gray_Normal                          
202004d7  Data_Tracker_Input                   
202004df  Flag_LED                             
202004e0  Motor                                
202004e8  Data_MotorEncoder                    
202004ec  Data_Motor_TarSpeed                  
202004f0  Data_Tracker_Offset                  
202004f4  __aeabi_errno                        
202004f8  delayTick                            
202004fc  uwTick                               
20200508  Flag_MPU6050_Ready                   
20200509  Gray_Digtal                          
2020050c  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[332 symbols]
