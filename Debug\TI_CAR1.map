******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 17:24:51 2025

OUTPUT FILE NAME:   <TI_CAR1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00007cfd


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  0000a408  00015bf8  R  X
  SRAM                  20200000   00008000  0000070c  000078f4  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    0000a408   0000a408    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000089d0   000089d0    r-x .text
  00008a90    00008a90    00001900   00001900    r-- .rodata
  0000a390    0000a390    00000078   00000078    r-- .cinit
20200000    20200000    0000050d   00000000    rw-
  20200000    20200000    000003d3   00000000    rw- .bss
  202003d4    202003d4    00000139   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000089d0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  00001364    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  0000159c    0000022c     MPU6050.o (.text.Read_Quad)
                  000017c8    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  000019f4    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001c14    000001fc     Task_App.o (.text.Task_Init)
                  00001e10    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00002004    000001e0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000021e4    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  000023c0    000001b0     Task.o (.text.Task_Start)
                  00002570    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  00002710    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000028a2    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000028a4    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00002a2c    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  00002bb4    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002d2c    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002e9c    00000154     MPU6050.o (.text.MPU6050_Init)
                  00002ff0    00000150     OLED.o (.text.OLED_Init)
                  00003140    0000014c     Task_App.o (.text.Task_OLED)
                  0000328c    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000033c8    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  000034fc    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00003630    00000130     OLED.o (.text.OLED_ShowChar)
                  00003760    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  00003890    00000128     Task_App.o (.text.Task_Tracker)
                  000039b8    00000128     inv_mpu.o (.text.mpu_init)
                  00003ae0    00000124     PID_IQMath.o (.text.PID_IQ_Prosc)
                  00003c04    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  00003d28    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00003e48    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003f54    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  0000405c    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00004160    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00004260    000000f0     Motor.o (.text.Motor_SetDirc)
                  00004350    000000f0     Task_App.o (.text.Task_Motor_PID)
                  00004440    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  0000452c    000000e4     Interrupt.o (.text.GROUP1_IRQHandler)
                  00004610    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000046f4    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  000047d8    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000048b4    000000dc     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  00004990    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00004a68    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00004b40    000000d4     inv_mpu.o (.text.set_int_enable)
                  00004c14    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  00004ce4    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  00004da8    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00004e6c    000000bc     Task_App.o (.text.System_SoftReset)
                  00004f28    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  00004fe4    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  0000509c    000000b4     Task.o (.text.Task_Add)
                  00005150    000000ac     Task_App.o (.text.Task_Serial)
                  000051fc    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  000052a8    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  00005354    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  000053fe    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00005400    000000a2                            : udivmoddi4.S.obj (.text)
                  000054a2    00000002     --HOLE-- [fill = 0]
                  000054a4    000000a0     Motor.o (.text.Motor_SetDuty)
                  00005544    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000055e4    0000009c     OLED.o (.text.OLED_IsPresent)
                  00005680    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  0000571c    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  000057b4    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  0000584c    00000096     MPU6050.o (.text.inv_row_2_scale)
                  000058e2    00000002     --HOLE-- [fill = 0]
                  000058e4    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_Motor_PWM_init)
                  00005970    0000008c     Task_App.o (.text.Task_Key)
                  000059fc    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00005a88    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00005b14    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00005b98    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00005c1c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00005c9e    00000002     --HOLE-- [fill = 0]
                  00005ca0    00000080     Motor.o (.text.Motor_GetSpeed)
                  00005d20    00000080     Task.o (.text.Task_Resume)
                  00005da0    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00005e1c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00005e90    00000074                            : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00005f04    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  00005f78    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00005fea    00000002     --HOLE-- [fill = 0]
                  00005fec    00000070     Serial.o (.text.MyPrintf_DMA)
                  0000605c    0000006e     OLED.o (.text.OLED_ShowString)
                  000060ca    00000002     --HOLE-- [fill = 0]
                  000060cc    0000006c     Motor.o (.text.Motor_Start)
                  00006138    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  000061a4    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  0000620e    00000002     --HOLE-- [fill = 0]
                  00006210    00000068     Task.o (.text.Task_Suspend)
                  00006278    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000062e0    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00006346    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  000063ac    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00006410    00000064     MPU6050.o (.text.MPU6050_IsPresent)
                  00006474    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000064d8    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000653a    00000002     --HOLE-- [fill = 0]
                  0000653c    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  0000659e    00000002     --HOLE-- [fill = 0]
                  000065a0    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00006600    00000060     Key_Led.o (.text.Key_Read)
                  00006660    00000060     Task_App.o (.text.Task_IdleFunction)
                  000066c0    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00006720    00000060     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00006780    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  000067e0    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  0000683e    00000002     --HOLE-- [fill = 0]
                  00006840    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  0000689c    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  000068f8    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  00006954    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  000069b0    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00006a08    00000058     Serial.o (.text.Serial_Init)
                  00006a60    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00006ab8    00000058            : _printfi.c.obj (.text._pconv_f)
                  00006b10    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00006b66    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00006bb8    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  00006c08    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  00006c58    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00006ca8    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  00006cf4    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00006d40    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00006d8c    0000004c     OLED.o (.text.OLED_Printf)
                  00006dd8    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00006e24    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00006e6e    00000002     --HOLE-- [fill = 0]
                  00006e70    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00006eba    00000002     --HOLE-- [fill = 0]
                  00006ebc    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00006f04    00000048     ADC.o (.text.adc_getValue)
                  00006f4c    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  00006f94    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  00006fdc    00000048     MPU6050.o (.text.mspm0_i2c_disable)
                  00007024    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00007068    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  000070ac    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  000070f0    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  00007134    00000044     OLED.o (.text.mspm0_i2c_disable)
                  00007178    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  000071ba    00000002     --HOLE-- [fill = 0]
                  000071bc    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  000071fe    00000002     --HOLE-- [fill = 0]
                  00007200    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00007240    00000040     Interrupt.o (.text.Interrupt_Init)
                  00007280    00000040     Task_App.o (.text.Task_GraySensor)
                  000072c0    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00007300    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00007340    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00007380    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  000073c0    0000003e     Task.o (.text.Task_CMP)
                  000073fe    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  0000743c    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00007478    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000074b4    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000074f0    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  0000752c    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00007568    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  000075a4    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  000075e0    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  0000761c    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00007658    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00007694    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  000076ce    00000002     --HOLE-- [fill = 0]
                  000076d0    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  0000770a    00000038     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Normalize_For_User)
                  00007742    00000002     --HOLE-- [fill = 0]
                  00007744    00000038     Task_App.o (.text.Task_LED)
                  0000777c    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  000077b4    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000077e8    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0000781c    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00007850    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  00007884    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  000078b6    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  000078e8    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  00007918    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00007948    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00007978    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  000079a8    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  000079d8    00000030            : vsnprintf.c.obj (.text._outs)
                  00007a08    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  00007a38    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  00007a68    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00007a94    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00007ac0    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00007aec    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00007b18    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  00007b42    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00007b6a    00000028     OLED.o (.text.DL_Common_updateReg)
                  00007b92    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00007bba    00000002     --HOLE-- [fill = 0]
                  00007bbc    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00007be4    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00007c0c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00007c34    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00007c5c    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00007c84    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00007cac    00000028     SysTick.o (.text.SysTick_Increasment)
                  00007cd4    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00007cfc    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00007d24    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00007d4a    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  00007d70    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00007d96    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00007dbc    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00007de0    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00007e04    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00007e28    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00007e4a    00000002     --HOLE-- [fill = 0]
                  00007e4c    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00007e6c    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00007e8c    00000020     SysTick.o (.text.Delay)
                  00007eac    00000020     main.o (.text.main)
                  00007ecc    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  00007eec    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00007f0a    00000002     --HOLE-- [fill = 0]
                  00007f0c    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00007f2a    00000002     --HOLE-- [fill = 0]
                  00007f2c    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  00007f48    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  00007f64    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00007f80    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00007f9c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00007fb8    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00007fd4    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00007ff0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  0000800c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00008028    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00008044    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00008060    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  0000807c    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00008098    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  000080b4    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  000080d0    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  000080ec    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00008108    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00008124    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00008140    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  0000815c    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  00008178    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00008190    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  000081a8    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  000081c0    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  000081d8    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  000081f0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00008208    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00008220    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00008238    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00008250    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00008268    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00008280    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00008298    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000082b0    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  000082c8    00000018     Motor.o (.text.DL_GPIO_setPins)
                  000082e0    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  000082f8    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00008310    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00008328    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00008340    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00008358    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00008370    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00008388    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000083a0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  000083b8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  000083d0    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  000083e8    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00008400    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00008418    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  00008430    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00008448    00000018     MPU6050.o (.text.DL_I2C_reset)
                  00008460    00000018     OLED.o (.text.DL_I2C_reset)
                  00008478    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00008490    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  000084a8    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  000084c0    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  000084d8    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000084f0    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00008508    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00008520    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00008538    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00008550    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  00008568    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00008580    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00008598    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  000085b0    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  000085c8    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  000085e0    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  000085f8    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00008610    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  00008628    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  00008640    00000018            : vsprintf.c.obj (.text._outs)
                  00008658    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  0000866e    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  00008684    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  0000869a    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  000086b0    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  000086c6    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  000086dc    00000016     OLED.o (.text.DL_GPIO_readPins)
                  000086f2    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00008708    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  0000871e    00000016     SysTick.o (.text.SysGetTick)
                  00008734    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000874a    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  0000875e    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00008772    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00008786    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  0000879a    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  000087ae    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  000087c2    00000002     --HOLE-- [fill = 0]
                  000087c4    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  000087d8    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  000087ec    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00008800    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00008814    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00008828    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  0000883c    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00008850    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00008864    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00008878    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  0000888c    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000088a0    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000088b2    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000088c4    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000088d6    00000010     ADC.o (.text.DL_ADC12_getStatus)
                  000088e6    00000002     --HOLE-- [fill = 0]
                  000088e8    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000088f8    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00008908    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00008918    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00008928    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00008936    00000002     --HOLE-- [fill = 0]
                  00008938    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00008946    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00008954    0000000e     MPU6050.o (.text.tap_cb)
                  00008962    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00008970    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  0000897c    0000000c     SysTick.o (.text.Sys_GetTick)
                  00008988    0000000c     Task.o (.text.Task_CheckNum)
                  00008994    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000899e    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  000089a8    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  000089b8    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  000089c2    00000002     --HOLE-- [fill = 0]
                  000089c4    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  000089d4    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  000089de    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  000089e8    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  000089f2    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  000089fc    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00008a0c    0000000a     libc.a : vsprintf.c.obj (.text._outc)
                  00008a16    0000000a     MPU6050.o (.text.android_orient_cb)
                  00008a20    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00008a28    00000008     Interrupt.o (.text.SysTick_Handler)
                  00008a30    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00008a38    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00008a40    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00008a46    00000002     --HOLE-- [fill = 0]
                  00008a48    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00008a58    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00008a5e    00000006            : exit.c.obj (.text:abort)
                  00008a64    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00008a68    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00008a6c    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00008a70    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00008a74    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00008a84    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00008a88    00000008     --HOLE-- [fill = 0]

.cinit     0    0000a390    00000078     
                  0000a390    00000050     (.cinit..data.load) [load image, compression = lzss]
                  0000a3e0    0000000c     (__TI_handler_table)
                  0000a3ec    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  0000a3f4    00000010     (__TI_cinit_table)
                  0000a404    00000004     --HOLE-- [fill = 0]

.rodata    0    00008a90    00001900     
                  00008a90    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  00009686    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00009c76    00000228     OLED_Font.o (.rodata.asc2_0806)
                  00009e9e    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00009ea0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00009fa1    00000007     Task_App.o (.rodata.str1.11952760121962574671.1)
                  00009fa8    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00009fe8    00000032     Task_App.o (.rodata.str1.3403810636401035402.1)
                  0000a01a    00000029     Task_App.o (.rodata.str1.4133793139133961309.1)
                  0000a043    00000001     --HOLE-- [fill = 0]
                  0000a044    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  0000a06c    00000028     inv_mpu.o (.rodata.test)
                  0000a094    00000027     Task_App.o (.rodata.str1.7950429023856218820.1)
                  0000a0bb    00000025     Task_App.o (.rodata.str1.5297265082290894444.1)
                  0000a0e0    00000024     Task_App.o (.rodata.str1.1200745476254391468.1)
                  0000a104    00000022     Task_App.o (.rodata.str1.5161480910995489644.1)
                  0000a126    00000020     Task_App.o (.rodata.str1.8896853068034818020.1)
                  0000a146    0000001f     Task_App.o (.rodata.str1.16339103789219331179.1)
                  0000a165    0000001f     Task_App.o (.rodata.str1.3850258909703972507.1)
                  0000a184    0000001e     inv_mpu.o (.rodata.reg)
                  0000a1a2    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  0000a1a4    0000001c     Task_App.o (.rodata..L__const.System_SoftReset.task_names)
                  0000a1c0    0000001a     Task_App.o (.rodata.str1.12629676409056169537.1)
                  0000a1da    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  0000a1dc    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  0000a1f4    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  0000a20c    00000018     Task_App.o (.rodata.str1.10635198597896025474.1)
                  0000a224    00000018     Task_App.o (.rodata.str1.6643214035752360150.1)
                  0000a23c    00000017     Task_App.o (.rodata.str1.3743034515018940988.1)
                  0000a253    00000014     OLED.o (.rodata.str1.10627719544674466389.1)
                  0000a267    00000014     Task_App.o (.rodata.str1.16389650136473743432.1)
                  0000a27b    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  0000a28c    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  0000a29d    00000011     Task_App.o (.rodata.str1.7346008805793267871.1)
                  0000a2ae    00000011     Task_App.o (.rodata.str1.9809402759195163968.1)
                  0000a2bf    00000010     Task_App.o (.rodata.str1.11373919790952722939.1)
                  0000a2cf    0000000f     Task_App.o (.rodata.str1.4374607466655451358.1)
                  0000a2de    0000000f     Task_App.o (.rodata.str1.7269909886346255147.1)
                  0000a2ed    0000000e     Task_App.o (.rodata.str1.5017135634981511656.1)
                  0000a2fb    0000000d     Task_App.o (.rodata.str1.13861004553356644102.1)
                  0000a308    0000000c     inv_mpu.o (.rodata.hw)
                  0000a314    0000000c     Task_App.o (.rodata.str1.12980382611605970010.1)
                  0000a320    0000000c     Task_App.o (.rodata.str1.13166305789289702848.1)
                  0000a32c    0000000c     OLED.o (.rodata.str1.475677112691578884.1)
                  0000a338    0000000b     Task_App.o (.rodata.str1.4769078833470683459.1)
                  0000a343    0000000b     Task_App.o (.rodata.str1.9092480108036065651.1)
                  0000a34e    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  0000a358    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  0000a360    00000008     ti_msp_dl_config.o (.rodata.gMotor_PWMConfig)
                  0000a368    00000008     Task_App.o (.rodata.str1.11683036942922059812.1)
                  0000a370    00000006     Task_App.o (.rodata.str1.16020955549137178199.1)
                  0000a376    00000005     Task_App.o (.rodata.str1.5883415095785080416.1)
                  0000a37b    00000004     Task_App.o (.rodata.str1.14074990341397557290.1)
                  0000a37f    00000004     Task_App.o (.rodata.str1.492715258893803702.1)
                  0000a383    00000003     ti_msp_dl_config.o (.rodata.gMotor_PWMClockConfig)
                  0000a386    00000003     Task_App.o (.rodata.str1.16301319874972139807.1)
                  0000a389    00000007     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000003d3     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000b0     (.common:GraySensor)
                  202003a0    00000010     (.common:quat)
                  202003b0    00000006     (.common:Data_Accel)
                  202003b6    00000006     (.common:Data_Gyro)
                  202003bc    00000004     (.common:Data_Pitch)
                  202003c0    00000004     (.common:Data_Roll)
                  202003c4    00000004     (.common:Data_Yaw)
                  202003c8    00000004     (.common:ExISR_Flag)
                  202003cc    00000004     (.common:sensor_timestamp)
                  202003d0    00000002     (.common:sensors)
                  202003d2    00000001     (.common:more)

.data      0    202003d4    00000139     UNINITIALIZED
                  202003d4    00000048     Motor.o (.data.Motor_Left)
                  2020041c    00000048     Motor.o (.data.Motor_Right)
                  20200464    0000002c     inv_mpu.o (.data.st)
                  20200490    00000010     Task_App.o (.data.Gray_Anolog)
                  202004a0    00000010     Task_App.o (.data.Gray_Normal)
                  202004b0    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  202004c0    0000000e     MPU6050.o (.data.hal)
                  202004ce    00000009     MPU6050.o (.data.gyro_orientation)
                  202004d7    00000008     Task_App.o (.data.Data_Tracker_Input)
                  202004df    00000001     Task_App.o (.data.Flag_LED)
                  202004e0    00000008     Task_App.o (.data.Motor)
                  202004e8    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202004ec    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202004f0    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202004f4    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004f8    00000004     SysTick.o (.data.delayTick)
                  202004fc    00000004     SysTick.o (.data.uwTick)
                  20200500    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  20200502    00000002     Task_App.o (.data.Task_Key.Key_Press_Count)
                  20200504    00000002     Task_App.o (.data.Task_OLED.oled_error_count)
                  20200506    00000002     Task_App.o (.data.Task_OLED.oled_reinit_timer)
                  20200508    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  20200509    00000001     Task_App.o (.data.Gray_Digtal)
                  2020050a    00000001     Task_App.o (.data.Task_Key.Key_Old)
                  2020050b    00000001     Task.o (.data.Task_Num)
                  2020050c    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               3426    123       0      
       startup_mspm0g350x_ticlang.o     8       192       0      
       main.o                           32      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3466    315       0      
                                                                 
    .\APP\Src\
       Task_App.o                       2136    666       247    
       Interrupt.o                      470     0         6      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2606    666       253    
                                                                 
    .\BSP\Src\
       MPU6050.o                        2584    0         70     
       OLED.o                           2074    32        0      
       OLED_Font.o                      0       2072      0      
       No_Mcu_Ganv_Grayscale_Sensor.o   1244    0         0      
       Task.o                           918     0         241    
       Serial.o                         404     0         512    
       Motor.o                          704     0         144    
       PID_IQMath.o                     402     0         0      
       ADC.o                            236     0         0      
       Key_Led.o                        118     0         0      
       SysTick.o                        106     0         8      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8790    2104      975    
                                                                 
    .\DMP\
       inv_mpu_dmp_motion_driver.o      3110    3062      16     
       inv_mpu.o                        4600    82        44     
    +--+--------------------------------+-------+---------+---------+
       Total:                           7710    3144      60     
                                                                 
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o     388     0         0      
       dl_timer.o                       356     0         0      
       dl_i2c.o                         192     0         0      
       dl_uart.o                        90      0         0      
       dl_dma.o                         76      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1176    0         0      
                                                                 
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/iqmath/lib/ticlang/m0p/mathacl/mspm0g1x0x_g3x0x/iqmath.a
       _IQNtoF.o                        48      0         0      
       _IQNdiv.o                        24      0         0      
       _IQNmpy.o                        24      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           96      0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   4510    34        0      
       e_asin.c.obj                     908     0         0      
       s_atan.c.obj                     784     64        0      
       e_atan2.c.obj                    392     0         0      
       e_sqrt.c.obj                     368     0         0      
       qsort.c.obj                      308     0         0      
       aeabi_ctype.S.obj                0       257       0      
       s_scalbn.c.obj                   216     0         0      
       vsnprintf.c.obj                  136     0         0      
       copy_decompress_lzss.c.obj       124     0         0      
       s_frexp.c.obj                    92      0         0      
       _ltoa.c.obj                      88      0         0      
       vsprintf.c.obj                   78      0         0      
       atoi.c.obj                       64      0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       memccpy.c.obj                    34      0         0      
       memcmp.c.obj                     32      0         0      
       copy_zero_init.c.obj             22      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       wcslen.c.obj                     16      0         0      
       memset16.S.obj                   14      0         0      
       aeabi_portable.c.obj             8       0         4      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8356    355       4      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     434     0         0      
       divdf3.S.obj                     284     0         0      
       muldf3.S.obj                     244     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       udivmoddi4.S.obj                 162     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       truncdfsf2.S.obj                 116     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       aeabi_idivmod.S.obj              86      0         0      
       fixdfsi.S.obj                    74      0         0      
       fixunsdfsi.S.obj                 66      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       extendsfdf2.S.obj                64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       muldi3.S.obj                     36      0         0      
       ashldi3.S.obj                    30      0         0      
       aeabi_uldivmod.S.obj             20      0         0      
       aeabi_memset.S.obj               14      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3020    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       116       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     35224   6700      1804   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 0000a3f4 records: 2, size/record: 8, table size: 16
	.data: load addr=0000a390, load size=00000050 bytes, run addr=202003d4, run size=00000139 bytes, compression=lzss
	.bss: load addr=0000a3ec, load size=00000008 bytes, run addr=20200000, run size=000003d3 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 0000a3e0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00002711     000089a8     000089a6   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00004611     000089c4     000089c0   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             000089dc          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             000089f0          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00008a26          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00008a5c          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00003e49     000089fc     000089fa   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   0000271b     00008a48     00008a44   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00008a6e          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00007cfd     00008a74     00008a70   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00008a65  ADC0_IRQHandler                      
00008a65  ADC1_IRQHandler                      
00008a65  AES_IRQHandler                       
00008a68  C$$EXIT                              
00008a65  CANFD0_IRQHandler                    
00008a65  DAC0_IRQHandler                      
00007201  DL_ADC12_setClockConfig              
00008995  DL_Common_delayCycles                
00006cf5  DL_DMA_initChannel                   
000067e1  DL_I2C_fillControllerTXFIFO          
000074f1  DL_I2C_flushControllerTXFIFO         
00007d97  DL_I2C_setClockConfig                
000047d9  DL_SYSCTL_configSYSPLL               
000063ad  DL_SYSCTL_setHFCLKSourceHFXTParams   
00007025  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
0000405d  DL_Timer_initFourCCPWMMode           
00008109  DL_Timer_setCaptCompUpdateMethod     
00008521  DL_Timer_setCaptureCompareOutCtl     
000088f9  DL_Timer_setCaptureCompareValue      
00008125  DL_Timer_setClockConfig              
00006ebd  DL_UART_init                         
000088a1  DL_UART_setClockConfig               
00008a65  DMA_IRQHandler                       
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202004e8  Data_MotorEncoder                    
202004ec  Data_Motor_TarSpeed                  
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202004d7  Data_Tracker_Input                   
202004f0  Data_Tracker_Offset                  
202003c4  Data_Yaw                             
00008a65  Default_Handler                      
00007e8d  Delay                                
202003c8  ExISR_Flag                           
202004df  Flag_LED                             
20200508  Flag_MPU6050_Ready                   
00008a65  GROUP0_IRQHandler                    
0000452d  GROUP1_IRQHandler                    
000048b5  Get_Analog_value                     
00007569  Get_Anolog_Value                     
00008929  Get_Digtal_For_User                  
0000770b  Get_Normalize_For_User               
202002f0  GraySensor                           
20200490  Gray_Anolog                          
20200509  Gray_Digtal                          
202004a0  Gray_Normal                          
00008a69  HOSTexit                             
00008a65  HardFault_Handler                    
00008a65  I2C0_IRQHandler                      
00008a65  I2C1_IRQHandler                      
000061a5  I2C_OLED_Clear                       
000075a5  I2C_OLED_Set_Pos                     
0000571d  I2C_OLED_WR_Byte                     
000065a1  I2C_OLED_i2c_sda_unlock              
00007241  Interrupt_Init                       
00006601  Key_Read                             
00002e9d  MPU6050_Init                         
00006411  MPU6050_IsPresent                    
202004e0  Motor                                
00005ca1  Motor_GetSpeed                       
202003d4  Motor_Left                           
2020041c  Motor_Right                          
000054a5  Motor_SetDuty                        
000060cd  Motor_Start                          
00005fed  MyPrintf_DMA                         
00008a65  NMI_Handler                          
000028a5  No_MCU_Ganv_Sensor_Init              
00005f79  No_MCU_Ganv_Sensor_Init_Frist        
00007179  No_Mcu_Ganv_Sensor_Task_Without_tick 
00002ff1  OLED_Init                            
000055e5  OLED_IsPresent                       
00006d8d  OLED_Printf                          
00003631  OLED_ShowChar                        
0000605d  OLED_ShowString                      
00007b19  PID_IQ_Init                          
00003ae1  PID_IQ_Prosc                         
00007069  PID_IQ_SetParams                     
00008a65  PendSV_Handler                       
00008a65  RTC_IRQHandler                       
0000159d  Read_Quad                            
00008a71  Reset_Handler                        
00008a65  SPI0_IRQHandler                      
00008a65  SPI1_IRQHandler                      
00008a65  SVC_Handler                          
00006dd9  SYSCFG_DL_ADC1_init                  
00007949  SYSCFG_DL_DMA_CH_RX_init             
000085e1  SYSCFG_DL_DMA_CH_TX_init             
00008971  SYSCFG_DL_DMA_init                   
00002005  SYSCFG_DL_GPIO_init                  
000069b1  SYSCFG_DL_I2C_MPU6050_init           
00006475  SYSCFG_DL_I2C_OLED_init              
000058e5  SYSCFG_DL_Motor_PWM_init             
00006841  SYSCFG_DL_SYSCTL_init                
00008909  SYSCFG_DL_SYSTICK_init               
00005b15  SYSCFG_DL_UART0_init                 
00007a69  SYSCFG_DL_init                       
00005545  SYSCFG_DL_initPower                  
00006a09  Serial_Init                          
20200000  Serial_RxData                        
0000871f  SysGetTick                           
00008a29  SysTick_Handler                      
00007cad  SysTick_Increasment                  
0000897d  Sys_GetTick                          
00004e6d  System_SoftReset                     
00008a65  TIMA0_IRQHandler                     
00008a65  TIMA1_IRQHandler                     
00008a65  TIMG0_IRQHandler                     
00008a65  TIMG12_IRQHandler                    
00008a65  TIMG6_IRQHandler                     
00008a65  TIMG7_IRQHandler                     
00008a65  TIMG8_IRQHandler                     
000088b3  TI_memcpy_small                      
00008963  TI_memset_small                      
0000509d  Task_Add                             
00008989  Task_CheckNum                        
00007281  Task_GraySensor                      
00006661  Task_IdleFunction                    
00001c15  Task_Init                            
00005971  Task_Key                             
00007745  Task_LED                             
00004351  Task_Motor_PID                       
00003141  Task_OLED                            
00005d21  Task_Resume                          
00005151  Task_Serial                          
000023c1  Task_Start                           
00006211  Task_Suspend                         
00003891  Task_Tracker                         
00008a65  UART0_IRQHandler                     
00008a65  UART1_IRQHandler                     
00008a65  UART2_IRQHandler                     
00008a65  UART3_IRQHandler                     
000085f9  _IQ24div                             
00008611  _IQ24mpy                             
00007979  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
0000a3f4  __TI_CINIT_Base                      
0000a404  __TI_CINIT_Limit                     
0000a404  __TI_CINIT_Warm                      
0000a3e0  __TI_Handler_Table_Base              
0000a3ec  __TI_Handler_Table_Limit             
00007659  __TI_auto_init_nobinit_nopinit       
00005da1  __TI_decompress_lzss                 
000088c5  __TI_decompress_none                 
00006a61  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00008735  __TI_zero_init_nomemset              
0000271b  __adddf3                             
00004a73  __addsf3                             
00009ea0  __aeabi_ctype_table_                 
00009ea0  __aeabi_ctype_table_C                
00005e91  __aeabi_d2f                          
00006e71  __aeabi_d2iz                         
000071bd  __aeabi_d2uiz                        
0000271b  __aeabi_dadd                         
000064d9  __aeabi_dcmpeq                       
00006515  __aeabi_dcmpge                       
00006529  __aeabi_dcmpgt                       
00006501  __aeabi_dcmple                       
000064ed  __aeabi_dcmplt                       
00003e49  __aeabi_ddiv                         
00004611  __aeabi_dmul                         
00002711  __aeabi_dsub                         
202004f4  __aeabi_errno                        
00008a31  __aeabi_errno_addr                   
00007301  __aeabi_f2d                          
0000777d  __aeabi_f2iz                         
00004a73  __aeabi_fadd                         
0000653d  __aeabi_fcmpeq                       
00006579  __aeabi_fcmpge                       
0000658d  __aeabi_fcmpgt                       
00006565  __aeabi_fcmple                       
00006551  __aeabi_fcmplt                       
00005c1d  __aeabi_fdiv                         
000059fd  __aeabi_fmul                         
00004a69  __aeabi_fsub                         
00007ac1  __aeabi_i2d                          
000075e1  __aeabi_i2f                          
00006b11  __aeabi_idiv                         
000028a3  __aeabi_idiv0                        
00006b11  __aeabi_idivmod                      
000053ff  __aeabi_ldiv0                        
00007f0d  __aeabi_llsl                         
00007e05  __aeabi_lmul                         
00008a39  __aeabi_memcpy                       
00008a39  __aeabi_memcpy4                      
00008a39  __aeabi_memcpy8                      
00008939  __aeabi_memset                       
00008939  __aeabi_memset4                      
00008939  __aeabi_memset8                      
00007de1  __aeabi_ui2d                         
00007cd5  __aeabi_ui2f                         
000072c1  __aeabi_uidiv                        
000072c1  __aeabi_uidivmod                     
00008851  __aeabi_uldivmod                     
00007f0d  __ashldi3                            
ffffffff  __binit__                            
00006279  __cmpdf2                             
00007695  __cmpsf2                             
00003e49  __divdf3                             
00005c1d  __divsf3                             
00006279  __eqdf2                              
00007695  __eqsf2                              
00007301  __extendsfdf2                        
00006e71  __fixdfsi                            
0000777d  __fixsfsi                            
000071bd  __fixunsdfsi                         
00007ac1  __floatsidf                          
000075e1  __floatsisf                          
00007de1  __floatunsidf                        
00007cd5  __floatunsisf                        
00005e1d  __gedf2                              
0000761d  __gesf2                              
00005e1d  __gtdf2                              
0000761d  __gtsf2                              
00006279  __ledf2                              
00007695  __lesf2                              
00006279  __ltdf2                              
00007695  __ltsf2                              
UNDEFED   __mpu_init                           
00004611  __muldf3                             
00007e05  __muldi3                             
000076d1  __muldsi3                            
000059fd  __mulsf3                             
00006279  __nedf2                              
00007695  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00002711  __subdf3                             
00004a69  __subsf3                             
00005e91  __truncdfsf2                         
00005401  __udivmoddi4                         
00007cfd  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00008a85  _system_pre_init                     
00008a5f  abort                                
00006f05  adc_getValue                         
00009c76  asc2_0806                            
00009686  asc2_1608                            
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00002a2d  atan2                                
00002a2d  atan2l                               
00000df5  atanl                                
00007341  atoi                                 
ffffffff  binit                                
00006139  convertAnalogToDigital               
202004f8  delayTick                            
00006f4d  dmp_enable_6x_lp_quat                
000010ed  dmp_enable_feature                   
000066c1  dmp_enable_gyro_cal                  
00006f95  dmp_enable_lp_quat                   
0000815d  dmp_load_motion_driver_firmware      
00001e11  dmp_read_fifo                        
00008865  dmp_register_android_orient_cb       
00008879  dmp_register_tap_cb                  
000057b5  dmp_set_fifo_rate                    
00002bb5  dmp_set_orientation                  
000070ad  dmp_set_shake_reject_thresh          
00007885  dmp_set_shake_reject_time            
000078b7  dmp_set_shake_reject_timeout         
00006347  dmp_set_tap_axes                     
000070f1  dmp_set_tap_count                    
00001365  dmp_set_tap_thresh                   
00007a09  dmp_set_tap_time                     
00007a39  dmp_set_tap_time_multi               
2020050c  enable_group1_irq                    
0000689d  frexp                                
0000689d  frexpl                               
0000a308  hw                                   
00000000  interruptVectors                     
00004991  ldexp                                
00004991  ldexpl                               
00007ead  main                                 
00007e29  memccpy                              
00007ecd  memcmp                               
202003d2  more                                 
00006721  mpu6050_i2c_sda_unlock               
00004f29  mpu_configure_fifo                   
00005f05  mpu_get_accel_fsr                    
00006781  mpu_get_gyro_fsr                     
00007851  mpu_get_sample_rate                  
000039b9  mpu_init                             
00003c05  mpu_load_firmware                    
00004161  mpu_lp_accel_mode                    
00003f55  mpu_read_fifo_stream                 
000051fd  mpu_read_mem                         
000017c9  mpu_reset_fifo                       
000046f5  mpu_set_accel_fsr                    
00002571  mpu_set_bypass                       
00004fe5  mpu_set_dmp_state                    
00004ce5  mpu_set_gyro_fsr                     
00005681  mpu_set_int_latched                  
00004c15  mpu_set_lpf                          
00004441  mpu_set_sample_rate                  
00003761  mpu_set_sensors                      
000052a9  mpu_write_mem                        
000033c9  mspm0_i2c_read                       
00004da9  mspm0_i2c_write                      
00005355  normalizeAnalogValues                
000034fd  qsort                                
202003a0  quat                                 
0000a184  reg                                  
00004991  scalbn                               
00004991  scalbnl                              
202003cc  sensor_timestamp                     
202003d0  sensors                              
00002d2d  sqrt                                 
00002d2d  sqrtl                                
0000a06c  test                                 
202004fc  uwTick                               
00007381  vsnprintf                            
00007aed  vsprintf                             
00008919  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  dmp_enable_feature                   
00001365  dmp_set_tap_thresh                   
0000159d  Read_Quad                            
000017c9  mpu_reset_fifo                       
00001c15  Task_Init                            
00001e11  dmp_read_fifo                        
00002005  SYSCFG_DL_GPIO_init                  
000023c1  Task_Start                           
00002571  mpu_set_bypass                       
00002711  __aeabi_dsub                         
00002711  __subdf3                             
0000271b  __adddf3                             
0000271b  __aeabi_dadd                         
000028a3  __aeabi_idiv0                        
000028a5  No_MCU_Ganv_Sensor_Init              
00002a2d  atan2                                
00002a2d  atan2l                               
00002bb5  dmp_set_orientation                  
00002d2d  sqrt                                 
00002d2d  sqrtl                                
00002e9d  MPU6050_Init                         
00002ff1  OLED_Init                            
00003141  Task_OLED                            
000033c9  mspm0_i2c_read                       
000034fd  qsort                                
00003631  OLED_ShowChar                        
00003761  mpu_set_sensors                      
00003891  Task_Tracker                         
000039b9  mpu_init                             
00003ae1  PID_IQ_Prosc                         
00003c05  mpu_load_firmware                    
00003e49  __aeabi_ddiv                         
00003e49  __divdf3                             
00003f55  mpu_read_fifo_stream                 
0000405d  DL_Timer_initFourCCPWMMode           
00004161  mpu_lp_accel_mode                    
00004351  Task_Motor_PID                       
00004441  mpu_set_sample_rate                  
0000452d  GROUP1_IRQHandler                    
00004611  __aeabi_dmul                         
00004611  __muldf3                             
000046f5  mpu_set_accel_fsr                    
000047d9  DL_SYSCTL_configSYSPLL               
000048b5  Get_Analog_value                     
00004991  ldexp                                
00004991  ldexpl                               
00004991  scalbn                               
00004991  scalbnl                              
00004a69  __aeabi_fsub                         
00004a69  __subsf3                             
00004a73  __addsf3                             
00004a73  __aeabi_fadd                         
00004c15  mpu_set_lpf                          
00004ce5  mpu_set_gyro_fsr                     
00004da9  mspm0_i2c_write                      
00004e6d  System_SoftReset                     
00004f29  mpu_configure_fifo                   
00004fe5  mpu_set_dmp_state                    
0000509d  Task_Add                             
00005151  Task_Serial                          
000051fd  mpu_read_mem                         
000052a9  mpu_write_mem                        
00005355  normalizeAnalogValues                
000053ff  __aeabi_ldiv0                        
00005401  __udivmoddi4                         
000054a5  Motor_SetDuty                        
00005545  SYSCFG_DL_initPower                  
000055e5  OLED_IsPresent                       
00005681  mpu_set_int_latched                  
0000571d  I2C_OLED_WR_Byte                     
000057b5  dmp_set_fifo_rate                    
000058e5  SYSCFG_DL_Motor_PWM_init             
00005971  Task_Key                             
000059fd  __aeabi_fmul                         
000059fd  __mulsf3                             
00005b15  SYSCFG_DL_UART0_init                 
00005c1d  __aeabi_fdiv                         
00005c1d  __divsf3                             
00005ca1  Motor_GetSpeed                       
00005d21  Task_Resume                          
00005da1  __TI_decompress_lzss                 
00005e1d  __gedf2                              
00005e1d  __gtdf2                              
00005e91  __aeabi_d2f                          
00005e91  __truncdfsf2                         
00005f05  mpu_get_accel_fsr                    
00005f79  No_MCU_Ganv_Sensor_Init_Frist        
00005fed  MyPrintf_DMA                         
0000605d  OLED_ShowString                      
000060cd  Motor_Start                          
00006139  convertAnalogToDigital               
000061a5  I2C_OLED_Clear                       
00006211  Task_Suspend                         
00006279  __cmpdf2                             
00006279  __eqdf2                              
00006279  __ledf2                              
00006279  __ltdf2                              
00006279  __nedf2                              
00006347  dmp_set_tap_axes                     
000063ad  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006411  MPU6050_IsPresent                    
00006475  SYSCFG_DL_I2C_OLED_init              
000064d9  __aeabi_dcmpeq                       
000064ed  __aeabi_dcmplt                       
00006501  __aeabi_dcmple                       
00006515  __aeabi_dcmpge                       
00006529  __aeabi_dcmpgt                       
0000653d  __aeabi_fcmpeq                       
00006551  __aeabi_fcmplt                       
00006565  __aeabi_fcmple                       
00006579  __aeabi_fcmpge                       
0000658d  __aeabi_fcmpgt                       
000065a1  I2C_OLED_i2c_sda_unlock              
00006601  Key_Read                             
00006661  Task_IdleFunction                    
000066c1  dmp_enable_gyro_cal                  
00006721  mpu6050_i2c_sda_unlock               
00006781  mpu_get_gyro_fsr                     
000067e1  DL_I2C_fillControllerTXFIFO          
00006841  SYSCFG_DL_SYSCTL_init                
0000689d  frexp                                
0000689d  frexpl                               
000069b1  SYSCFG_DL_I2C_MPU6050_init           
00006a09  Serial_Init                          
00006a61  __TI_ltoa                            
00006b11  __aeabi_idiv                         
00006b11  __aeabi_idivmod                      
00006cf5  DL_DMA_initChannel                   
00006d8d  OLED_Printf                          
00006dd9  SYSCFG_DL_ADC1_init                  
00006e71  __aeabi_d2iz                         
00006e71  __fixdfsi                            
00006ebd  DL_UART_init                         
00006f05  adc_getValue                         
00006f4d  dmp_enable_6x_lp_quat                
00006f95  dmp_enable_lp_quat                   
00007025  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00007069  PID_IQ_SetParams                     
000070ad  dmp_set_shake_reject_thresh          
000070f1  dmp_set_tap_count                    
00007179  No_Mcu_Ganv_Sensor_Task_Without_tick 
000071bd  __aeabi_d2uiz                        
000071bd  __fixunsdfsi                         
00007201  DL_ADC12_setClockConfig              
00007241  Interrupt_Init                       
00007281  Task_GraySensor                      
000072c1  __aeabi_uidiv                        
000072c1  __aeabi_uidivmod                     
00007301  __aeabi_f2d                          
00007301  __extendsfdf2                        
00007341  atoi                                 
00007381  vsnprintf                            
000074f1  DL_I2C_flushControllerTXFIFO         
00007569  Get_Anolog_Value                     
000075a5  I2C_OLED_Set_Pos                     
000075e1  __aeabi_i2f                          
000075e1  __floatsisf                          
0000761d  __gesf2                              
0000761d  __gtsf2                              
00007659  __TI_auto_init_nobinit_nopinit       
00007695  __cmpsf2                             
00007695  __eqsf2                              
00007695  __lesf2                              
00007695  __ltsf2                              
00007695  __nesf2                              
000076d1  __muldsi3                            
0000770b  Get_Normalize_For_User               
00007745  Task_LED                             
0000777d  __aeabi_f2iz                         
0000777d  __fixsfsi                            
00007851  mpu_get_sample_rate                  
00007885  dmp_set_shake_reject_time            
000078b7  dmp_set_shake_reject_timeout         
00007949  SYSCFG_DL_DMA_CH_RX_init             
00007979  _IQ24toF                             
00007a09  dmp_set_tap_time                     
00007a39  dmp_set_tap_time_multi               
00007a69  SYSCFG_DL_init                       
00007ac1  __aeabi_i2d                          
00007ac1  __floatsidf                          
00007aed  vsprintf                             
00007b19  PID_IQ_Init                          
00007cad  SysTick_Increasment                  
00007cd5  __aeabi_ui2f                         
00007cd5  __floatunsisf                        
00007cfd  _c_int00_noargs                      
00007d97  DL_I2C_setClockConfig                
00007de1  __aeabi_ui2d                         
00007de1  __floatunsidf                        
00007e05  __aeabi_lmul                         
00007e05  __muldi3                             
00007e29  memccpy                              
00007e8d  Delay                                
00007ead  main                                 
00007ecd  memcmp                               
00007f0d  __aeabi_llsl                         
00007f0d  __ashldi3                            
00008109  DL_Timer_setCaptCompUpdateMethod     
00008125  DL_Timer_setClockConfig              
0000815d  dmp_load_motion_driver_firmware      
00008521  DL_Timer_setCaptureCompareOutCtl     
000085e1  SYSCFG_DL_DMA_CH_TX_init             
000085f9  _IQ24div                             
00008611  _IQ24mpy                             
0000871f  SysGetTick                           
00008735  __TI_zero_init_nomemset              
00008851  __aeabi_uldivmod                     
00008865  dmp_register_android_orient_cb       
00008879  dmp_register_tap_cb                  
000088a1  DL_UART_setClockConfig               
000088b3  TI_memcpy_small                      
000088c5  __TI_decompress_none                 
000088f9  DL_Timer_setCaptureCompareValue      
00008909  SYSCFG_DL_SYSTICK_init               
00008919  wcslen                               
00008929  Get_Digtal_For_User                  
00008939  __aeabi_memset                       
00008939  __aeabi_memset4                      
00008939  __aeabi_memset8                      
00008963  TI_memset_small                      
00008971  SYSCFG_DL_DMA_init                   
0000897d  Sys_GetTick                          
00008989  Task_CheckNum                        
00008995  DL_Common_delayCycles                
00008a29  SysTick_Handler                      
00008a31  __aeabi_errno_addr                   
00008a39  __aeabi_memcpy                       
00008a39  __aeabi_memcpy4                      
00008a39  __aeabi_memcpy8                      
00008a5f  abort                                
00008a65  ADC0_IRQHandler                      
00008a65  ADC1_IRQHandler                      
00008a65  AES_IRQHandler                       
00008a65  CANFD0_IRQHandler                    
00008a65  DAC0_IRQHandler                      
00008a65  DMA_IRQHandler                       
00008a65  Default_Handler                      
00008a65  GROUP0_IRQHandler                    
00008a65  HardFault_Handler                    
00008a65  I2C0_IRQHandler                      
00008a65  I2C1_IRQHandler                      
00008a65  NMI_Handler                          
00008a65  PendSV_Handler                       
00008a65  RTC_IRQHandler                       
00008a65  SPI0_IRQHandler                      
00008a65  SPI1_IRQHandler                      
00008a65  SVC_Handler                          
00008a65  TIMA0_IRQHandler                     
00008a65  TIMA1_IRQHandler                     
00008a65  TIMG0_IRQHandler                     
00008a65  TIMG12_IRQHandler                    
00008a65  TIMG6_IRQHandler                     
00008a65  TIMG7_IRQHandler                     
00008a65  TIMG8_IRQHandler                     
00008a65  UART0_IRQHandler                     
00008a65  UART1_IRQHandler                     
00008a65  UART2_IRQHandler                     
00008a65  UART3_IRQHandler                     
00008a68  C$$EXIT                              
00008a69  HOSTexit                             
00008a71  Reset_Handler                        
00008a85  _system_pre_init                     
00009686  asc2_1608                            
00009c76  asc2_0806                            
00009ea0  __aeabi_ctype_table_                 
00009ea0  __aeabi_ctype_table_C                
0000a06c  test                                 
0000a184  reg                                  
0000a308  hw                                   
0000a3e0  __TI_Handler_Table_Base              
0000a3ec  __TI_Handler_Table_Limit             
0000a3f4  __TI_CINIT_Base                      
0000a404  __TI_CINIT_Limit                     
0000a404  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  GraySensor                           
202003a0  quat                                 
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202003c4  Data_Yaw                             
202003c8  ExISR_Flag                           
202003cc  sensor_timestamp                     
202003d0  sensors                              
202003d2  more                                 
202003d4  Motor_Left                           
2020041c  Motor_Right                          
20200490  Gray_Anolog                          
202004a0  Gray_Normal                          
202004d7  Data_Tracker_Input                   
202004df  Flag_LED                             
202004e0  Motor                                
202004e8  Data_MotorEncoder                    
202004ec  Data_Motor_TarSpeed                  
202004f0  Data_Tracker_Offset                  
202004f4  __aeabi_errno                        
202004f8  delayTick                            
202004fc  uwTick                               
20200508  Flag_MPU6050_Ready                   
20200509  Gray_Digtal                          
2020050c  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[332 symbols]
