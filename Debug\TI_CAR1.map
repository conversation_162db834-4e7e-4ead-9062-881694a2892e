******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 17:58:42 2025

OUTPUT FILE NAME:   <TI_CAR1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 0000815d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  0000aa88  00015578  R  X
  SRAM                  20200000   00008000  00000721  000078df  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    0000aa88   0000aa88    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00008e20   00008e20    r-x .text
  00008ee0    00008ee0    00001b30   00001b30    r-- .rodata
  0000aa10    0000aa10    00000078   00000078    r-- .cinit
20200000    20200000    00000522   00000000    rw-
  20200000    20200000    000003d3   00000000    rw- .bss
  202003d4    202003d4    0000014e   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00008e20     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  00001364    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  0000159c    0000022c     OLED.o (.text.OLED_Init)
                  000017c8    0000022c     MPU6050.o (.text.Read_Quad)
                  000019f4    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  00001c20    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001e40    00000218     Task_App.o (.text.Task_Init)
                  00002058    00000200     Task_App.o (.text.Task_OLED)
                  00002258    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  0000244c    000001e0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000262c    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00002808    000001b0     Task.o (.text.Task_Start)
                  000029b8    000001ac     Task_App.o (.text.Task_OLED_Monitor)
                  00002b64    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  00002d04    00000198     OLED.o (.text.OLED_IsPresent)
                  00002e9c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000302e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00003030    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  000031b8    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  00003340    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  000034b8    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00003628    00000154     MPU6050.o (.text.MPU6050_Init)
                  0000377c    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000038b8    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  000039ec    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00003b20    00000130     OLED.o (.text.OLED_ShowChar)
                  00003c50    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  00003d80    00000128     Task_App.o (.text.Task_Tracker)
                  00003ea8    00000128     inv_mpu.o (.text.mpu_init)
                  00003fd0    00000124     PID_IQMath.o (.text.PID_IQ_Prosc)
                  000040f4    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  00004218    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00004338    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00004444    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  0000454c    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00004650    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00004750    000000f0     Motor.o (.text.Motor_SetDirc)
                  00004840    000000f0     Task_App.o (.text.Task_Motor_PID)
                  00004930    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  00004a1c    000000e4     Interrupt.o (.text.GROUP1_IRQHandler)
                  00004b00    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00004be4    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  00004cc8    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00004da4    000000dc     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  00004e80    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00004f58    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00005030    000000d4     inv_mpu.o (.text.set_int_enable)
                  00005104    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  000051d4    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  00005298    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  0000535c    000000bc     Task_App.o (.text.System_SoftReset)
                  00005418    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  000054d4    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  0000558c    000000b4     Task.o (.text.Task_Add)
                  00005640    000000ac     Task_App.o (.text.Task_Serial)
                  000056ec    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  00005798    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  00005844    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  000058ee    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  000058f0    000000a2                            : udivmoddi4.S.obj (.text)
                  00005992    00000002     --HOLE-- [fill = 0]
                  00005994    000000a0     Motor.o (.text.Motor_SetDuty)
                  00005a34    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00005ad4    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  00005b70    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  00005c08    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  00005ca0    00000096     MPU6050.o (.text.inv_row_2_scale)
                  00005d36    00000002     --HOLE-- [fill = 0]
                  00005d38    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_Motor_PWM_init)
                  00005dc4    0000008c     Task_App.o (.text.Task_Key)
                  00005e50    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00005edc    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00005f68    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00005fec    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00006070    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  000060f2    00000002     --HOLE-- [fill = 0]
                  000060f4    00000080     Motor.o (.text.Motor_GetSpeed)
                  00006174    00000080     Task.o (.text.Task_Resume)
                  000061f4    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00006270    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000062e4    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  000062f0    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00006364    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  000063d8    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  0000644a    00000002     --HOLE-- [fill = 0]
                  0000644c    00000070     Serial.o (.text.MyPrintf_DMA)
                  000064bc    0000006e     OLED.o (.text.OLED_ShowString)
                  0000652a    00000002     --HOLE-- [fill = 0]
                  0000652c    0000006c     Motor.o (.text.Motor_Start)
                  00006598    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00006604    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  0000666e    00000002     --HOLE-- [fill = 0]
                  00006670    00000068     Task.o (.text.Task_Suspend)
                  000066d8    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00006740    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  000067a6    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  0000680c    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00006870    00000064     MPU6050.o (.text.MPU6050_IsPresent)
                  000068d4    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00006938    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000699a    00000002     --HOLE-- [fill = 0]
                  0000699c    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  000069fe    00000002     --HOLE-- [fill = 0]
                  00006a00    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00006a60    00000060     Key_Led.o (.text.Key_Read)
                  00006ac0    00000060     Task_App.o (.text.Task_IdleFunction)
                  00006b20    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00006b80    00000060     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00006be0    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  00006c40    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00006c9e    00000002     --HOLE-- [fill = 0]
                  00006ca0    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00006cfc    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00006d58    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  00006db4    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00006e10    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00006e68    00000058     Serial.o (.text.Serial_Init)
                  00006ec0    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00006f18    00000058            : _printfi.c.obj (.text._pconv_f)
                  00006f70    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00006fc6    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00007018    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  00007068    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  000070b8    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00007108    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  00007154    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  000071a0    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000071ec    0000004c     OLED.o (.text.OLED_Printf)
                  00007238    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00007284    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  000072ce    00000002     --HOLE-- [fill = 0]
                  000072d0    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  0000731a    00000002     --HOLE-- [fill = 0]
                  0000731c    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00007364    00000048     ADC.o (.text.adc_getValue)
                  000073ac    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  000073f4    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  0000743c    00000048     MPU6050.o (.text.mspm0_i2c_disable)
                  00007484    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000074c8    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  0000750c    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  00007550    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  00007594    00000044     OLED.o (.text.mspm0_i2c_disable)
                  000075d8    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  0000761a    00000002     --HOLE-- [fill = 0]
                  0000761c    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  0000765e    00000002     --HOLE-- [fill = 0]
                  00007660    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  000076a0    00000040     Interrupt.o (.text.Interrupt_Init)
                  000076e0    00000040     Task_App.o (.text.Task_GraySensor)
                  00007720    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00007760    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000077a0    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000077e0    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00007820    0000003e     Task.o (.text.Task_CMP)
                  0000785e    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  0000789c    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000078d8    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00007914    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00007950    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  0000798c    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000079c8    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00007a04    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00007a40    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00007a7c    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00007ab8    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00007af4    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00007b2e    00000002     --HOLE-- [fill = 0]
                  00007b30    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00007b6a    00000038     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Normalize_For_User)
                  00007ba2    00000002     --HOLE-- [fill = 0]
                  00007ba4    00000038     Task_App.o (.text.Task_LED)
                  00007bdc    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00007c14    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00007c48    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00007c7c    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00007cb0    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  00007ce4    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  00007d16    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  00007d48    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  00007d78    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00007da8    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00007dd8    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00007e08    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00007e38    00000030            : vsnprintf.c.obj (.text._outs)
                  00007e68    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  00007e98    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  00007ec8    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00007ef4    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00007f20    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00007f4c    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00007f78    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  00007fa2    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00007fca    00000028     OLED.o (.text.DL_Common_updateReg)
                  00007ff2    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  0000801a    00000002     --HOLE-- [fill = 0]
                  0000801c    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00008044    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  0000806c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00008094    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  000080bc    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  000080e4    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  0000810c    00000028     SysTick.o (.text.SysTick_Increasment)
                  00008134    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  0000815c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00008184    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  000081aa    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  000081d0    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  000081f6    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  0000821c    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00008240    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00008264    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00008288    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  000082aa    00000002     --HOLE-- [fill = 0]
                  000082ac    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  000082cc    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  000082ec    00000020     SysTick.o (.text.Delay)
                  0000830c    00000020     main.o (.text.main)
                  0000832c    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  0000834c    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  0000836a    00000002     --HOLE-- [fill = 0]
                  0000836c    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  0000838a    00000002     --HOLE-- [fill = 0]
                  0000838c    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  000083a8    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  000083c4    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  000083e0    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  000083fc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00008418    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00008434    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00008450    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  0000846c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00008488    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000084a4    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  000084c0    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  000084dc    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  000084f8    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00008514    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00008530    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  0000854c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00008568    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00008584    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000085a0    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000085bc    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  000085d8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  000085f0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00008608    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  00008620    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  00008638    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00008650    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00008668    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00008680    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00008698    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  000086b0    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  000086c8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000086e0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  000086f8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00008710    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  00008728    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00008740    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00008758    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00008770    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00008788    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000087a0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  000087b8    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  000087d0    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  000087e8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00008800    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00008818    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00008830    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  00008848    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00008860    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00008878    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  00008890    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  000088a8    00000018     MPU6050.o (.text.DL_I2C_reset)
                  000088c0    00000018     OLED.o (.text.DL_I2C_reset)
                  000088d8    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  000088f0    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00008908    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00008920    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00008938    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00008950    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00008968    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00008980    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00008998    00000018     Motor.o (.text.DL_Timer_startCounter)
                  000089b0    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  000089c8    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  000089e0    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  000089f8    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00008a10    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00008a28    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00008a40    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00008a58    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00008a70    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  00008a88    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  00008aa0    00000018            : vsprintf.c.obj (.text._outs)
                  00008ab8    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  00008ace    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  00008ae4    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00008afa    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00008b10    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  00008b26    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  00008b3c    00000016     OLED.o (.text.DL_GPIO_readPins)
                  00008b52    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00008b68    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00008b7e    00000016     SysTick.o (.text.SysGetTick)
                  00008b94    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00008baa    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  00008bbe    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00008bd2    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00008be6    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00008bfa    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  00008c0e    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00008c22    00000002     --HOLE-- [fill = 0]
                  00008c24    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00008c38    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00008c4c    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00008c60    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00008c74    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00008c88    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00008c9c    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00008cb0    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00008cc4    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00008cd8    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  00008cec    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00008d00    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00008d12    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00008d24    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00008d36    00000010     ADC.o (.text.DL_ADC12_getStatus)
                  00008d46    00000002     --HOLE-- [fill = 0]
                  00008d48    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00008d58    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00008d68    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00008d78    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00008d88    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00008d96    00000002     --HOLE-- [fill = 0]
                  00008d98    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00008da6    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00008db4    0000000e     MPU6050.o (.text.tap_cb)
                  00008dc2    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00008dd0    0000000c     SysTick.o (.text.Sys_GetTick)
                  00008ddc    0000000c     Task.o (.text.Task_CheckNum)
                  00008de8    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00008df2    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00008dfc    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00008e0c    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00008e16    00000002     --HOLE-- [fill = 0]
                  00008e18    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00008e28    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00008e32    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00008e3c    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00008e46    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00008e50    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00008e60    0000000a     libc.a : vsprintf.c.obj (.text._outc)
                  00008e6a    0000000a     MPU6050.o (.text.android_orient_cb)
                  00008e74    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00008e7c    00000008     Interrupt.o (.text.SysTick_Handler)
                  00008e84    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00008e8c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00008e94    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00008e9a    00000002     --HOLE-- [fill = 0]
                  00008e9c    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00008eac    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00008eb2    00000006            : exit.c.obj (.text:abort)
                  00008eb8    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00008ebc    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00008ec0    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00008ec4    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00008ec8    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00008ed8    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00008edc    00000004     --HOLE-- [fill = 0]

.cinit     0    0000aa10    00000078     
                  0000aa10    00000050     (.cinit..data.load) [load image, compression = lzss]
                  0000aa60    0000000c     (__TI_handler_table)
                  0000aa6c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  0000aa74    00000010     (__TI_cinit_table)
                  0000aa84    00000004     --HOLE-- [fill = 0]

.rodata    0    00008ee0    00001b30     
                  00008ee0    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  00009ad6    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  0000a0c6    00000228     OLED_Font.o (.rodata.asc2_0806)
                  0000a2ee    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  0000a2f0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  0000a3f1    00000007     Task_App.o (.rodata.str1.11952760121962574671.1)
                  0000a3f8    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  0000a438    00000032     Task_App.o (.rodata.str1.10836333124219633692.1)
                  0000a46a    00000032     Task_App.o (.rodata.str1.9809402759195163968.1)
                  0000a49c    0000002e     Task_App.o (.rodata.str1.11579078728849559700.1)
                  0000a4ca    0000002b     Task_App.o (.rodata.str1.4769078833470683459.1)
                  0000a4f5    0000002a     Task_App.o (.rodata.str1.2769847933961235050.1)
                  0000a51f    00000001     --HOLE-- [fill = 0]
                  0000a520    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  0000a548    00000028     inv_mpu.o (.rodata.test)
                  0000a570    00000027     Task_App.o (.rodata.str1.13166305789289702848.1)
                  0000a597    00000027     Task_App.o (.rodata.str1.16826070253670079193.1)
                  0000a5be    00000026     Task_App.o (.rodata.str1.7269909886346255147.1)
                  0000a5e4    00000025     Task_App.o (.rodata.str1.13879218764332898332.1)
                  0000a609    00000025     Task_App.o (.rodata.str1.4133793139133961309.1)
                  0000a62e    00000025     Task_App.o (.rodata.str1.4374607466655451358.1)
                  0000a653    00000024     Task_App.o (.rodata.str1.5297265082290894444.1)
                  0000a677    00000022     Task_App.o (.rodata.str1.1200745476254391468.1)
                  0000a699    00000022     Task_App.o (.rodata.str1.16339103789219331179.1)
                  0000a6bb    00000020     Task_App.o (.rodata.str1.11983945721906562862.1)
                  0000a6db    00000020     Task_App.o (.rodata.str1.8896853068034818020.1)
                  0000a6fb    0000001f     Task_App.o (.rodata.str1.7946239225661045399.1)
                  0000a71a    0000001e     inv_mpu.o (.rodata.reg)
                  0000a738    0000001e     Task_App.o (.rodata.str1.16389650136473743432.1)
                  0000a756    0000001d     Task_App.o (.rodata.str1.7457130010273042766.1)
                  0000a773    00000001     --HOLE-- [fill = 0]
                  0000a774    0000001c     Task_App.o (.rodata..L__const.System_SoftReset.task_names)
                  0000a790    0000001c     Task_App.o (.rodata.str1.6643214035752360150.1)
                  0000a7ac    0000001b     Task_App.o (.rodata.str1.12406033377069493089.1)
                  0000a7c7    0000001b     Task_App.o (.rodata.str1.3403810636401035402.1)
                  0000a7e2    0000001a     Task_App.o (.rodata.str1.12629676409056169537.1)
                  0000a7fc    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  0000a814    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  0000a82c    00000018     Task_App.o (.rodata.str1.10635198597896025474.1)
                  0000a844    00000017     Task_App.o (.rodata.str1.3743034515018940988.1)
                  0000a85b    00000017     Task_App.o (.rodata.str1.4837728415194742158.1)
                  0000a872    00000014     Task_App.o (.rodata.str1.11357868258216044683.1)
                  0000a886    00000013     Task_App.o (.rodata.str1.12678017295864298256.1)
                  0000a899    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  0000a8aa    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  0000a8bb    00000011     Task_App.o (.rodata.str1.5161480910995489644.1)
                  0000a8cc    00000011     Task_App.o (.rodata.str1.764873899199215101.1)
                  0000a8dd    00000011     Task_App.o (.rodata.str1.8463153030208135045.1)
                  0000a8ee    00000010     Task_App.o (.rodata.str1.9092480108036065651.1)
                  0000a8fe    0000000f     Task_App.o (.rodata.str1.10206427874968570540.1)
                  0000a90d    0000000f     Task_App.o (.rodata.str1.10611952666245981902.1)
                  0000a91c    0000000f     Task_App.o (.rodata.str1.6965739551501383038.1)
                  0000a92b    0000000f     Task_App.o (.rodata.str1.9922084053193859316.1)
                  0000a93a    0000000e     OLED.o (.rodata.str1.475677112691578884.1)
                  0000a948    0000000e     Task_App.o (.rodata.str1.7346008805793267871.1)
                  0000a956    0000000d     Task_App.o (.rodata.str1.11373919790952722939.1)
                  0000a963    0000000d     Task_App.o (.rodata.str1.3850258909703972507.1)
                  0000a970    0000000d     Task_App.o (.rodata.str1.7160865309107172604.1)
                  0000a97d    00000001     --HOLE-- [fill = 0]
                  0000a97e    0000000c     inv_mpu.o (.rodata.hw)
                  0000a98a    0000000c     OLED.o (.rodata.str1.10627719544674466389.1)
                  0000a996    0000000c     Task_App.o (.rodata.str1.13861004553356644102.1)
                  0000a9a2    0000000c     Task_App.o (.rodata.str1.5017135634981511656.1)
                  0000a9ae    0000000b     Task_App.o (.rodata.str1.16301319874972139807.1)
                  0000a9b9    0000000b     Task_App.o (.rodata.str1.7950429023856218820.1)
                  0000a9c4    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  0000a9ce    0000000a     Task_App.o (.rodata.str1.12747617577557512136.1)
                  0000a9d8    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  0000a9e0    00000008     ti_msp_dl_config.o (.rodata.gMotor_PWMConfig)
                  0000a9e8    00000008     Task_App.o (.rodata.str1.11683036942922059812.1)
                  0000a9f0    00000006     Task_App.o (.rodata.str1.16020955549137178199.1)
                  0000a9f6    00000005     Task_App.o (.rodata.str1.5883415095785080416.1)
                  0000a9fb    00000004     Task_App.o (.rodata.str1.14074990341397557290.1)
                  0000a9ff    00000004     Task_App.o (.rodata.str1.492715258893803702.1)
                  0000aa03    00000003     ti_msp_dl_config.o (.rodata.gMotor_PWMClockConfig)
                  0000aa06    00000003     Task_App.o (.rodata.str1.12980382611605970010.1)
                  0000aa09    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  0000aa0b    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  0000aa0d    00000003     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000003d3     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000b0     (.common:GraySensor)
                  202003a0    00000010     (.common:quat)
                  202003b0    00000006     (.common:Data_Accel)
                  202003b6    00000006     (.common:Data_Gyro)
                  202003bc    00000004     (.common:Data_Pitch)
                  202003c0    00000004     (.common:Data_Roll)
                  202003c4    00000004     (.common:Data_Yaw)
                  202003c8    00000004     (.common:ExISR_Flag)
                  202003cc    00000004     (.common:sensor_timestamp)
                  202003d0    00000002     (.common:sensors)
                  202003d2    00000001     (.common:more)

.data      0    202003d4    0000014e     UNINITIALIZED
                  202003d4    00000048     Motor.o (.data.Motor_Left)
                  2020041c    00000048     Motor.o (.data.Motor_Right)
                  20200464    0000002c     inv_mpu.o (.data.st)
                  20200490    00000010     Task_App.o (.data.Gray_Anolog)
                  202004a0    00000010     Task_App.o (.data.Gray_Normal)
                  202004b0    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  202004c0    0000000e     MPU6050.o (.data.hal)
                  202004ce    00000009     MPU6050.o (.data.gyro_orientation)
                  202004d7    00000008     Task_App.o (.data.Data_Tracker_Input)
                  202004df    00000001     Task_App.o (.data.Flag_LED)
                  202004e0    00000008     Task_App.o (.data.Motor)
                  202004e8    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202004ec    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202004f0    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202004f4    00000004     Task_App.o (.data.Task_OLED_Monitor.failure_count)
                  202004f8    00000004     Task_App.o (.data.Task_OLED_Monitor.last_report_time)
                  202004fc    00000004     Task_App.o (.data.Task_OLED_Monitor.total_checks)
                  20200500    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200504    00000004     SysTick.o (.data.delayTick)
                  20200508    00000004     SysTick.o (.data.uwTick)
                  2020050c    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  2020050e    00000002     Task_App.o (.data.Task_Key.Key_Press_Count)
                  20200510    00000002     Task_App.o (.data.Task_OLED.display_refresh_counter)
                  20200512    00000002     Task_App.o (.data.Task_OLED.oled_error_count)
                  20200514    00000002     Task_App.o (.data.Task_OLED.oled_reinit_timer)
                  20200516    00000002     Task_App.o (.data.Task_OLED.oled_stable_count)
                  20200518    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  20200519    00000001     Task_App.o (.data.Gray_Digtal)
                  2020051a    00000001     OLED.o (.data.OLED_IsPresent.consecutive_failures)
                  2020051b    00000001     OLED.o (.data.OLED_IsPresent.consecutive_success)
                  2020051c    00000001     OLED.o (.data.OLED_IsPresent.last_stable_result)
                  2020051d    00000001     Task_App.o (.data.Task_Key.Key_Old)
                  2020051e    00000001     Task.o (.data.Task_Num)
                  2020051f    00000001     Task_App.o (.data.Task_OLED.oled_last_state)
                  20200520    00000001     Task_App.o (.data.Task_OLED_Monitor.last_oled_state)
                  20200521    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               3426    123       0      
       startup_mspm0g350x_ticlang.o     8       192       0      
       main.o                           32      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3466    315       0      
                                                                 
    .\APP\Src\
       Task_App.o                       2772    1234      265    
       Interrupt.o                      470     0         6      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3242    1234      271    
                                                                 
    .\BSP\Src\
       MPU6050.o                        2584    0         70     
       OLED.o                           2546    26        3      
       OLED_Font.o                      0       2072      0      
       No_Mcu_Ganv_Grayscale_Sensor.o   1244    0         0      
       Task.o                           918     0         241    
       Serial.o                         404     0         512    
       Motor.o                          704     0         144    
       PID_IQMath.o                     402     0         0      
       ADC.o                            236     0         0      
       Key_Led.o                        118     0         0      
       SysTick.o                        106     0         8      
    +--+--------------------------------+-------+---------+---------+
       Total:                           9262    2098      978    
                                                                 
    .\DMP\
       inv_mpu_dmp_motion_driver.o      3110    3062      16     
       inv_mpu.o                        4600    82        44     
    +--+--------------------------------+-------+---------+---------+
       Total:                           7710    3144      60     
                                                                 
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o     388     0         0      
       dl_timer.o                       356     0         0      
       dl_i2c.o                         192     0         0      
       dl_uart.o                        90      0         0      
       dl_dma.o                         76      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1176    0         0      
                                                                 
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/iqmath/lib/ticlang/m0p/mathacl/mspm0g1x0x_g3x0x/iqmath.a
       _IQNtoF.o                        48      0         0      
       _IQNdiv.o                        24      0         0      
       _IQNmpy.o                        24      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           96      0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   4510    34        0      
       e_asin.c.obj                     908     0         0      
       s_atan.c.obj                     784     64        0      
       e_atan2.c.obj                    392     0         0      
       e_sqrt.c.obj                     368     0         0      
       qsort.c.obj                      308     0         0      
       aeabi_ctype.S.obj                0       257       0      
       s_scalbn.c.obj                   216     0         0      
       vsnprintf.c.obj                  136     0         0      
       copy_decompress_lzss.c.obj       124     0         0      
       s_frexp.c.obj                    92      0         0      
       _ltoa.c.obj                      88      0         0      
       vsprintf.c.obj                   78      0         0      
       atoi.c.obj                       64      0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       memccpy.c.obj                    34      0         0      
       memcmp.c.obj                     32      0         0      
       copy_zero_init.c.obj             22      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       wcslen.c.obj                     16      0         0      
       memset16.S.obj                   14      0         0      
       aeabi_portable.c.obj             8       0         4      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8356    355       4      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     434     0         0      
       divdf3.S.obj                     284     0         0      
       muldf3.S.obj                     244     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       udivmoddi4.S.obj                 162     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       truncdfsf2.S.obj                 116     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       aeabi_idivmod.S.obj              86      0         0      
       fixdfsi.S.obj                    74      0         0      
       fixunsdfsi.S.obj                 66      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       extendsfdf2.S.obj                64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       muldi3.S.obj                     36      0         0      
       ashldi3.S.obj                    30      0         0      
       aeabi_uldivmod.S.obj             20      0         0      
       aeabi_memset.S.obj               14      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3020    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       116       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     36332   7262      1825   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 0000aa74 records: 2, size/record: 8, table size: 16
	.data: load addr=0000aa10, load size=00000050 bytes, run addr=202003d4, run size=0000014e bytes, compression=lzss
	.bss: load addr=0000aa6c, load size=00000008 bytes, run addr=20200000, run size=000003d3 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 0000aa60 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00002e9d     00008dfc     00008dfa   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00004b01     00008e18     00008e14   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00008e30          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00008e44          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00008e7a          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00008eb0          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00004339     00008e50     00008e4e   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00002ea7     00008e9c     00008e98   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00008ec2          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   0000815d     00008ec8     00008ec4   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00008eb9  ADC0_IRQHandler                      
00008eb9  ADC1_IRQHandler                      
00008eb9  AES_IRQHandler                       
00008ebc  C$$EXIT                              
00008eb9  CANFD0_IRQHandler                    
00008eb9  DAC0_IRQHandler                      
00007661  DL_ADC12_setClockConfig              
00008de9  DL_Common_delayCycles                
00007155  DL_DMA_initChannel                   
00006c41  DL_I2C_fillControllerTXFIFO          
00007951  DL_I2C_flushControllerTXFIFO         
000081f7  DL_I2C_setClockConfig                
00004cc9  DL_SYSCTL_configSYSPLL               
0000680d  DL_SYSCTL_setHFCLKSourceHFXTParams   
00007485  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
0000454d  DL_Timer_initFourCCPWMMode           
00008569  DL_Timer_setCaptCompUpdateMethod     
00008981  DL_Timer_setCaptureCompareOutCtl     
00008d59  DL_Timer_setCaptureCompareValue      
00008585  DL_Timer_setClockConfig              
0000731d  DL_UART_init                         
00008d01  DL_UART_setClockConfig               
00008eb9  DMA_IRQHandler                       
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202004e8  Data_MotorEncoder                    
202004ec  Data_Motor_TarSpeed                  
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202004d7  Data_Tracker_Input                   
202004f0  Data_Tracker_Offset                  
202003c4  Data_Yaw                             
00008eb9  Default_Handler                      
000082ed  Delay                                
202003c8  ExISR_Flag                           
202004df  Flag_LED                             
20200518  Flag_MPU6050_Ready                   
00008eb9  GROUP0_IRQHandler                    
00004a1d  GROUP1_IRQHandler                    
00004da5  Get_Analog_value                     
000079c9  Get_Anolog_Value                     
00008d89  Get_Digtal_For_User                  
00007b6b  Get_Normalize_For_User               
202002f0  GraySensor                           
20200490  Gray_Anolog                          
20200519  Gray_Digtal                          
202004a0  Gray_Normal                          
00008ebd  HOSTexit                             
00008eb9  HardFault_Handler                    
00008eb9  I2C0_IRQHandler                      
00008eb9  I2C1_IRQHandler                      
00006605  I2C_OLED_Clear                       
00007a05  I2C_OLED_Set_Pos                     
00005b71  I2C_OLED_WR_Byte                     
00006a01  I2C_OLED_i2c_sda_unlock              
000076a1  Interrupt_Init                       
00006a61  Key_Read                             
00003629  MPU6050_Init                         
00006871  MPU6050_IsPresent                    
202004e0  Motor                                
000060f5  Motor_GetSpeed                       
202003d4  Motor_Left                           
2020041c  Motor_Right                          
00005995  Motor_SetDuty                        
0000652d  Motor_Start                          
0000644d  MyPrintf_DMA                         
00008eb9  NMI_Handler                          
00003031  No_MCU_Ganv_Sensor_Init              
000063d9  No_MCU_Ganv_Sensor_Init_Frist        
000075d9  No_Mcu_Ganv_Sensor_Task_Without_tick 
0000159d  OLED_Init                            
00002d05  OLED_IsPresent                       
000071ed  OLED_Printf                          
00003b21  OLED_ShowChar                        
000064bd  OLED_ShowString                      
00007f79  PID_IQ_Init                          
00003fd1  PID_IQ_Prosc                         
000074c9  PID_IQ_SetParams                     
00008eb9  PendSV_Handler                       
00008eb9  RTC_IRQHandler                       
000017c9  Read_Quad                            
00008ec5  Reset_Handler                        
00008eb9  SPI0_IRQHandler                      
00008eb9  SPI1_IRQHandler                      
00008eb9  SVC_Handler                          
00007239  SYSCFG_DL_ADC1_init                  
00007da9  SYSCFG_DL_DMA_CH_RX_init             
00008a41  SYSCFG_DL_DMA_CH_TX_init             
000062e5  SYSCFG_DL_DMA_init                   
0000244d  SYSCFG_DL_GPIO_init                  
00006e11  SYSCFG_DL_I2C_MPU6050_init           
000068d5  SYSCFG_DL_I2C_OLED_init              
00005d39  SYSCFG_DL_Motor_PWM_init             
00006ca1  SYSCFG_DL_SYSCTL_init                
00008d69  SYSCFG_DL_SYSTICK_init               
00005f69  SYSCFG_DL_UART0_init                 
00007ec9  SYSCFG_DL_init                       
00005a35  SYSCFG_DL_initPower                  
00006e69  Serial_Init                          
20200000  Serial_RxData                        
00008b7f  SysGetTick                           
00008e7d  SysTick_Handler                      
0000810d  SysTick_Increasment                  
00008dd1  Sys_GetTick                          
0000535d  System_SoftReset                     
00008eb9  TIMA0_IRQHandler                     
00008eb9  TIMA1_IRQHandler                     
00008eb9  TIMG0_IRQHandler                     
00008eb9  TIMG12_IRQHandler                    
00008eb9  TIMG6_IRQHandler                     
00008eb9  TIMG7_IRQHandler                     
00008eb9  TIMG8_IRQHandler                     
00008d13  TI_memcpy_small                      
00008dc3  TI_memset_small                      
0000558d  Task_Add                             
00008ddd  Task_CheckNum                        
000076e1  Task_GraySensor                      
00006ac1  Task_IdleFunction                    
00001e41  Task_Init                            
00005dc5  Task_Key                             
00007ba5  Task_LED                             
00004841  Task_Motor_PID                       
00002059  Task_OLED                            
000029b9  Task_OLED_Monitor                    
00006175  Task_Resume                          
00005641  Task_Serial                          
00002809  Task_Start                           
00006671  Task_Suspend                         
00003d81  Task_Tracker                         
00008eb9  UART0_IRQHandler                     
00008eb9  UART1_IRQHandler                     
00008eb9  UART2_IRQHandler                     
00008eb9  UART3_IRQHandler                     
00008a59  _IQ24div                             
00008a71  _IQ24mpy                             
00007dd9  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
0000aa74  __TI_CINIT_Base                      
0000aa84  __TI_CINIT_Limit                     
0000aa84  __TI_CINIT_Warm                      
0000aa60  __TI_Handler_Table_Base              
0000aa6c  __TI_Handler_Table_Limit             
00007ab9  __TI_auto_init_nobinit_nopinit       
000061f5  __TI_decompress_lzss                 
00008d25  __TI_decompress_none                 
00006ec1  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00008b95  __TI_zero_init_nomemset              
00002ea7  __adddf3                             
00004f63  __addsf3                             
0000a2f0  __aeabi_ctype_table_                 
0000a2f0  __aeabi_ctype_table_C                
000062f1  __aeabi_d2f                          
000072d1  __aeabi_d2iz                         
0000761d  __aeabi_d2uiz                        
00002ea7  __aeabi_dadd                         
00006939  __aeabi_dcmpeq                       
00006975  __aeabi_dcmpge                       
00006989  __aeabi_dcmpgt                       
00006961  __aeabi_dcmple                       
0000694d  __aeabi_dcmplt                       
00004339  __aeabi_ddiv                         
00004b01  __aeabi_dmul                         
00002e9d  __aeabi_dsub                         
20200500  __aeabi_errno                        
00008e85  __aeabi_errno_addr                   
00007761  __aeabi_f2d                          
00007bdd  __aeabi_f2iz                         
00004f63  __aeabi_fadd                         
0000699d  __aeabi_fcmpeq                       
000069d9  __aeabi_fcmpge                       
000069ed  __aeabi_fcmpgt                       
000069c5  __aeabi_fcmple                       
000069b1  __aeabi_fcmplt                       
00006071  __aeabi_fdiv                         
00005e51  __aeabi_fmul                         
00004f59  __aeabi_fsub                         
00007f21  __aeabi_i2d                          
00007a41  __aeabi_i2f                          
00006f71  __aeabi_idiv                         
0000302f  __aeabi_idiv0                        
00006f71  __aeabi_idivmod                      
000058ef  __aeabi_ldiv0                        
0000836d  __aeabi_llsl                         
00008265  __aeabi_lmul                         
00008e8d  __aeabi_memcpy                       
00008e8d  __aeabi_memcpy4                      
00008e8d  __aeabi_memcpy8                      
00008d99  __aeabi_memset                       
00008d99  __aeabi_memset4                      
00008d99  __aeabi_memset8                      
00008241  __aeabi_ui2d                         
00008135  __aeabi_ui2f                         
00007721  __aeabi_uidiv                        
00007721  __aeabi_uidivmod                     
00008cb1  __aeabi_uldivmod                     
0000836d  __ashldi3                            
ffffffff  __binit__                            
000066d9  __cmpdf2                             
00007af5  __cmpsf2                             
00004339  __divdf3                             
00006071  __divsf3                             
000066d9  __eqdf2                              
00007af5  __eqsf2                              
00007761  __extendsfdf2                        
000072d1  __fixdfsi                            
00007bdd  __fixsfsi                            
0000761d  __fixunsdfsi                         
00007f21  __floatsidf                          
00007a41  __floatsisf                          
00008241  __floatunsidf                        
00008135  __floatunsisf                        
00006271  __gedf2                              
00007a7d  __gesf2                              
00006271  __gtdf2                              
00007a7d  __gtsf2                              
000066d9  __ledf2                              
00007af5  __lesf2                              
000066d9  __ltdf2                              
00007af5  __ltsf2                              
UNDEFED   __mpu_init                           
00004b01  __muldf3                             
00008265  __muldi3                             
00007b31  __muldsi3                            
00005e51  __mulsf3                             
000066d9  __nedf2                              
00007af5  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00002e9d  __subdf3                             
00004f59  __subsf3                             
000062f1  __truncdfsf2                         
000058f1  __udivmoddi4                         
0000815d  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00008ed9  _system_pre_init                     
00008eb3  abort                                
00007365  adc_getValue                         
0000a0c6  asc2_0806                            
00009ad6  asc2_1608                            
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
000031b9  atan2                                
000031b9  atan2l                               
00000df5  atanl                                
000077a1  atoi                                 
ffffffff  binit                                
00006599  convertAnalogToDigital               
20200504  delayTick                            
000073ad  dmp_enable_6x_lp_quat                
000010ed  dmp_enable_feature                   
00006b21  dmp_enable_gyro_cal                  
000073f5  dmp_enable_lp_quat                   
000085bd  dmp_load_motion_driver_firmware      
00002259  dmp_read_fifo                        
00008cc5  dmp_register_android_orient_cb       
00008cd9  dmp_register_tap_cb                  
00005c09  dmp_set_fifo_rate                    
00003341  dmp_set_orientation                  
0000750d  dmp_set_shake_reject_thresh          
00007ce5  dmp_set_shake_reject_time            
00007d17  dmp_set_shake_reject_timeout         
000067a7  dmp_set_tap_axes                     
00007551  dmp_set_tap_count                    
00001365  dmp_set_tap_thresh                   
00007e69  dmp_set_tap_time                     
00007e99  dmp_set_tap_time_multi               
20200521  enable_group1_irq                    
00006cfd  frexp                                
00006cfd  frexpl                               
0000a97e  hw                                   
00000000  interruptVectors                     
00004e81  ldexp                                
00004e81  ldexpl                               
0000830d  main                                 
00008289  memccpy                              
0000832d  memcmp                               
202003d2  more                                 
00006b81  mpu6050_i2c_sda_unlock               
00005419  mpu_configure_fifo                   
00006365  mpu_get_accel_fsr                    
00006be1  mpu_get_gyro_fsr                     
00007cb1  mpu_get_sample_rate                  
00003ea9  mpu_init                             
000040f5  mpu_load_firmware                    
00004651  mpu_lp_accel_mode                    
00004445  mpu_read_fifo_stream                 
000056ed  mpu_read_mem                         
000019f5  mpu_reset_fifo                       
00004be5  mpu_set_accel_fsr                    
00002b65  mpu_set_bypass                       
000054d5  mpu_set_dmp_state                    
000051d5  mpu_set_gyro_fsr                     
00005ad5  mpu_set_int_latched                  
00005105  mpu_set_lpf                          
00004931  mpu_set_sample_rate                  
00003c51  mpu_set_sensors                      
00005799  mpu_write_mem                        
000038b9  mspm0_i2c_read                       
00005299  mspm0_i2c_write                      
00005845  normalizeAnalogValues                
000039ed  qsort                                
202003a0  quat                                 
0000a71a  reg                                  
00004e81  scalbn                               
00004e81  scalbnl                              
202003cc  sensor_timestamp                     
202003d0  sensors                              
000034b9  sqrt                                 
000034b9  sqrtl                                
0000a548  test                                 
20200508  uwTick                               
000077e1  vsnprintf                            
00007f4d  vsprintf                             
00008d79  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  dmp_enable_feature                   
00001365  dmp_set_tap_thresh                   
0000159d  OLED_Init                            
000017c9  Read_Quad                            
000019f5  mpu_reset_fifo                       
00001e41  Task_Init                            
00002059  Task_OLED                            
00002259  dmp_read_fifo                        
0000244d  SYSCFG_DL_GPIO_init                  
00002809  Task_Start                           
000029b9  Task_OLED_Monitor                    
00002b65  mpu_set_bypass                       
00002d05  OLED_IsPresent                       
00002e9d  __aeabi_dsub                         
00002e9d  __subdf3                             
00002ea7  __adddf3                             
00002ea7  __aeabi_dadd                         
0000302f  __aeabi_idiv0                        
00003031  No_MCU_Ganv_Sensor_Init              
000031b9  atan2                                
000031b9  atan2l                               
00003341  dmp_set_orientation                  
000034b9  sqrt                                 
000034b9  sqrtl                                
00003629  MPU6050_Init                         
000038b9  mspm0_i2c_read                       
000039ed  qsort                                
00003b21  OLED_ShowChar                        
00003c51  mpu_set_sensors                      
00003d81  Task_Tracker                         
00003ea9  mpu_init                             
00003fd1  PID_IQ_Prosc                         
000040f5  mpu_load_firmware                    
00004339  __aeabi_ddiv                         
00004339  __divdf3                             
00004445  mpu_read_fifo_stream                 
0000454d  DL_Timer_initFourCCPWMMode           
00004651  mpu_lp_accel_mode                    
00004841  Task_Motor_PID                       
00004931  mpu_set_sample_rate                  
00004a1d  GROUP1_IRQHandler                    
00004b01  __aeabi_dmul                         
00004b01  __muldf3                             
00004be5  mpu_set_accel_fsr                    
00004cc9  DL_SYSCTL_configSYSPLL               
00004da5  Get_Analog_value                     
00004e81  ldexp                                
00004e81  ldexpl                               
00004e81  scalbn                               
00004e81  scalbnl                              
00004f59  __aeabi_fsub                         
00004f59  __subsf3                             
00004f63  __addsf3                             
00004f63  __aeabi_fadd                         
00005105  mpu_set_lpf                          
000051d5  mpu_set_gyro_fsr                     
00005299  mspm0_i2c_write                      
0000535d  System_SoftReset                     
00005419  mpu_configure_fifo                   
000054d5  mpu_set_dmp_state                    
0000558d  Task_Add                             
00005641  Task_Serial                          
000056ed  mpu_read_mem                         
00005799  mpu_write_mem                        
00005845  normalizeAnalogValues                
000058ef  __aeabi_ldiv0                        
000058f1  __udivmoddi4                         
00005995  Motor_SetDuty                        
00005a35  SYSCFG_DL_initPower                  
00005ad5  mpu_set_int_latched                  
00005b71  I2C_OLED_WR_Byte                     
00005c09  dmp_set_fifo_rate                    
00005d39  SYSCFG_DL_Motor_PWM_init             
00005dc5  Task_Key                             
00005e51  __aeabi_fmul                         
00005e51  __mulsf3                             
00005f69  SYSCFG_DL_UART0_init                 
00006071  __aeabi_fdiv                         
00006071  __divsf3                             
000060f5  Motor_GetSpeed                       
00006175  Task_Resume                          
000061f5  __TI_decompress_lzss                 
00006271  __gedf2                              
00006271  __gtdf2                              
000062e5  SYSCFG_DL_DMA_init                   
000062f1  __aeabi_d2f                          
000062f1  __truncdfsf2                         
00006365  mpu_get_accel_fsr                    
000063d9  No_MCU_Ganv_Sensor_Init_Frist        
0000644d  MyPrintf_DMA                         
000064bd  OLED_ShowString                      
0000652d  Motor_Start                          
00006599  convertAnalogToDigital               
00006605  I2C_OLED_Clear                       
00006671  Task_Suspend                         
000066d9  __cmpdf2                             
000066d9  __eqdf2                              
000066d9  __ledf2                              
000066d9  __ltdf2                              
000066d9  __nedf2                              
000067a7  dmp_set_tap_axes                     
0000680d  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006871  MPU6050_IsPresent                    
000068d5  SYSCFG_DL_I2C_OLED_init              
00006939  __aeabi_dcmpeq                       
0000694d  __aeabi_dcmplt                       
00006961  __aeabi_dcmple                       
00006975  __aeabi_dcmpge                       
00006989  __aeabi_dcmpgt                       
0000699d  __aeabi_fcmpeq                       
000069b1  __aeabi_fcmplt                       
000069c5  __aeabi_fcmple                       
000069d9  __aeabi_fcmpge                       
000069ed  __aeabi_fcmpgt                       
00006a01  I2C_OLED_i2c_sda_unlock              
00006a61  Key_Read                             
00006ac1  Task_IdleFunction                    
00006b21  dmp_enable_gyro_cal                  
00006b81  mpu6050_i2c_sda_unlock               
00006be1  mpu_get_gyro_fsr                     
00006c41  DL_I2C_fillControllerTXFIFO          
00006ca1  SYSCFG_DL_SYSCTL_init                
00006cfd  frexp                                
00006cfd  frexpl                               
00006e11  SYSCFG_DL_I2C_MPU6050_init           
00006e69  Serial_Init                          
00006ec1  __TI_ltoa                            
00006f71  __aeabi_idiv                         
00006f71  __aeabi_idivmod                      
00007155  DL_DMA_initChannel                   
000071ed  OLED_Printf                          
00007239  SYSCFG_DL_ADC1_init                  
000072d1  __aeabi_d2iz                         
000072d1  __fixdfsi                            
0000731d  DL_UART_init                         
00007365  adc_getValue                         
000073ad  dmp_enable_6x_lp_quat                
000073f5  dmp_enable_lp_quat                   
00007485  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000074c9  PID_IQ_SetParams                     
0000750d  dmp_set_shake_reject_thresh          
00007551  dmp_set_tap_count                    
000075d9  No_Mcu_Ganv_Sensor_Task_Without_tick 
0000761d  __aeabi_d2uiz                        
0000761d  __fixunsdfsi                         
00007661  DL_ADC12_setClockConfig              
000076a1  Interrupt_Init                       
000076e1  Task_GraySensor                      
00007721  __aeabi_uidiv                        
00007721  __aeabi_uidivmod                     
00007761  __aeabi_f2d                          
00007761  __extendsfdf2                        
000077a1  atoi                                 
000077e1  vsnprintf                            
00007951  DL_I2C_flushControllerTXFIFO         
000079c9  Get_Anolog_Value                     
00007a05  I2C_OLED_Set_Pos                     
00007a41  __aeabi_i2f                          
00007a41  __floatsisf                          
00007a7d  __gesf2                              
00007a7d  __gtsf2                              
00007ab9  __TI_auto_init_nobinit_nopinit       
00007af5  __cmpsf2                             
00007af5  __eqsf2                              
00007af5  __lesf2                              
00007af5  __ltsf2                              
00007af5  __nesf2                              
00007b31  __muldsi3                            
00007b6b  Get_Normalize_For_User               
00007ba5  Task_LED                             
00007bdd  __aeabi_f2iz                         
00007bdd  __fixsfsi                            
00007cb1  mpu_get_sample_rate                  
00007ce5  dmp_set_shake_reject_time            
00007d17  dmp_set_shake_reject_timeout         
00007da9  SYSCFG_DL_DMA_CH_RX_init             
00007dd9  _IQ24toF                             
00007e69  dmp_set_tap_time                     
00007e99  dmp_set_tap_time_multi               
00007ec9  SYSCFG_DL_init                       
00007f21  __aeabi_i2d                          
00007f21  __floatsidf                          
00007f4d  vsprintf                             
00007f79  PID_IQ_Init                          
0000810d  SysTick_Increasment                  
00008135  __aeabi_ui2f                         
00008135  __floatunsisf                        
0000815d  _c_int00_noargs                      
000081f7  DL_I2C_setClockConfig                
00008241  __aeabi_ui2d                         
00008241  __floatunsidf                        
00008265  __aeabi_lmul                         
00008265  __muldi3                             
00008289  memccpy                              
000082ed  Delay                                
0000830d  main                                 
0000832d  memcmp                               
0000836d  __aeabi_llsl                         
0000836d  __ashldi3                            
00008569  DL_Timer_setCaptCompUpdateMethod     
00008585  DL_Timer_setClockConfig              
000085bd  dmp_load_motion_driver_firmware      
00008981  DL_Timer_setCaptureCompareOutCtl     
00008a41  SYSCFG_DL_DMA_CH_TX_init             
00008a59  _IQ24div                             
00008a71  _IQ24mpy                             
00008b7f  SysGetTick                           
00008b95  __TI_zero_init_nomemset              
00008cb1  __aeabi_uldivmod                     
00008cc5  dmp_register_android_orient_cb       
00008cd9  dmp_register_tap_cb                  
00008d01  DL_UART_setClockConfig               
00008d13  TI_memcpy_small                      
00008d25  __TI_decompress_none                 
00008d59  DL_Timer_setCaptureCompareValue      
00008d69  SYSCFG_DL_SYSTICK_init               
00008d79  wcslen                               
00008d89  Get_Digtal_For_User                  
00008d99  __aeabi_memset                       
00008d99  __aeabi_memset4                      
00008d99  __aeabi_memset8                      
00008dc3  TI_memset_small                      
00008dd1  Sys_GetTick                          
00008ddd  Task_CheckNum                        
00008de9  DL_Common_delayCycles                
00008e7d  SysTick_Handler                      
00008e85  __aeabi_errno_addr                   
00008e8d  __aeabi_memcpy                       
00008e8d  __aeabi_memcpy4                      
00008e8d  __aeabi_memcpy8                      
00008eb3  abort                                
00008eb9  ADC0_IRQHandler                      
00008eb9  ADC1_IRQHandler                      
00008eb9  AES_IRQHandler                       
00008eb9  CANFD0_IRQHandler                    
00008eb9  DAC0_IRQHandler                      
00008eb9  DMA_IRQHandler                       
00008eb9  Default_Handler                      
00008eb9  GROUP0_IRQHandler                    
00008eb9  HardFault_Handler                    
00008eb9  I2C0_IRQHandler                      
00008eb9  I2C1_IRQHandler                      
00008eb9  NMI_Handler                          
00008eb9  PendSV_Handler                       
00008eb9  RTC_IRQHandler                       
00008eb9  SPI0_IRQHandler                      
00008eb9  SPI1_IRQHandler                      
00008eb9  SVC_Handler                          
00008eb9  TIMA0_IRQHandler                     
00008eb9  TIMA1_IRQHandler                     
00008eb9  TIMG0_IRQHandler                     
00008eb9  TIMG12_IRQHandler                    
00008eb9  TIMG6_IRQHandler                     
00008eb9  TIMG7_IRQHandler                     
00008eb9  TIMG8_IRQHandler                     
00008eb9  UART0_IRQHandler                     
00008eb9  UART1_IRQHandler                     
00008eb9  UART2_IRQHandler                     
00008eb9  UART3_IRQHandler                     
00008ebc  C$$EXIT                              
00008ebd  HOSTexit                             
00008ec5  Reset_Handler                        
00008ed9  _system_pre_init                     
00009ad6  asc2_1608                            
0000a0c6  asc2_0806                            
0000a2f0  __aeabi_ctype_table_                 
0000a2f0  __aeabi_ctype_table_C                
0000a548  test                                 
0000a71a  reg                                  
0000a97e  hw                                   
0000aa60  __TI_Handler_Table_Base              
0000aa6c  __TI_Handler_Table_Limit             
0000aa74  __TI_CINIT_Base                      
0000aa84  __TI_CINIT_Limit                     
0000aa84  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  GraySensor                           
202003a0  quat                                 
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202003c4  Data_Yaw                             
202003c8  ExISR_Flag                           
202003cc  sensor_timestamp                     
202003d0  sensors                              
202003d2  more                                 
202003d4  Motor_Left                           
2020041c  Motor_Right                          
20200490  Gray_Anolog                          
202004a0  Gray_Normal                          
202004d7  Data_Tracker_Input                   
202004df  Flag_LED                             
202004e0  Motor                                
202004e8  Data_MotorEncoder                    
202004ec  Data_Motor_TarSpeed                  
202004f0  Data_Tracker_Offset                  
20200500  __aeabi_errno                        
20200504  delayTick                            
20200508  uwTick                               
20200518  Flag_MPU6050_Ready                   
20200519  Gray_Digtal                          
20200521  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[333 symbols]
