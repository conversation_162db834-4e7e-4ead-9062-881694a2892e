/**
 * @file OLED_Diagnostic_Tool.c
 * @brief OLED诊断工具 - 解决反复黑屏问题
 * @details 提供全面的OLED状态监控和故障诊断功能
 * <AUTHOR> Assistant
 * @date 2025-08-01
 */

#include "SysConfig.h"

// OLED诊断状态结构体
typedef struct {
    uint32_t total_checks;          // 总检查次数
    uint32_t success_count;         // 成功次数
    uint32_t failure_count;         // 失败次数
    uint32_t i2c_timeout_count;     // I2C超时次数
    uint32_t reinit_count;          // 重新初始化次数
    uint32_t consecutive_failures;  // 连续失败次数
    uint32_t max_consecutive_failures; // 最大连续失败次数
    uint32_t last_failure_time;     // 最后失败时间
    uint32_t last_success_time;     // 最后成功时间
    bool is_stable;                 // 是否稳定
    float success_rate;             // 成功率
} OLED_Diagnostic_t;

static OLED_Diagnostic_t oled_diag = {0};

/**
 * @brief OLED诊断检查
 * @return true: OLED正常, false: OLED异常
 */
bool OLED_DiagnosticCheck(void)
{
    oled_diag.total_checks++;
    uint32_t current_time = uwTick;
    
    // 检查I2C总线状态
    if (DL_I2C_getSDAStatus(I2C_OLED_INST) == DL_I2C_CONTROLLER_SDA_LOW) {
        MyPrintf_DMA("DIAG: I2C SDA line stuck LOW, attempting recovery...\r\n");
        I2C_OLED_i2c_sda_unlock();
        Delay(50);
    }
    
    // 检查OLED是否存在
    bool oled_present = OLED_IsPresent();
    
    if (oled_present) {
        oled_diag.success_count++;
        oled_diag.consecutive_failures = 0;
        oled_diag.last_success_time = current_time;
        
        // 尝试简单的显示测试
        I2C_OLED_WR_Byte(0xAE, I2C_OLED_CMD); // 关闭显示
        Delay(10);
        I2C_OLED_WR_Byte(0xAF, I2C_OLED_CMD); // 开启显示
        
        oled_diag.is_stable = (oled_diag.consecutive_failures == 0);
        
    } else {
        oled_diag.failure_count++;
        oled_diag.consecutive_failures++;
        oled_diag.last_failure_time = current_time;
        
        if (oled_diag.consecutive_failures > oled_diag.max_consecutive_failures) {
            oled_diag.max_consecutive_failures = oled_diag.consecutive_failures;
        }
        
        oled_diag.is_stable = false;
        
        MyPrintf_DMA("DIAG: OLED check failed, consecutive failures: %d\r\n", 
                     oled_diag.consecutive_failures);
    }
    
    // 计算成功率
    if (oled_diag.total_checks > 0) {
        oled_diag.success_rate = (float)oled_diag.success_count / oled_diag.total_checks * 100.0f;
    }
    
    return oled_present;
}

/**
 * @brief 强制OLED恢复
 * @return true: 恢复成功, false: 恢复失败
 */
bool OLED_ForceRecovery(void)
{
    MyPrintf_DMA("DIAG: Starting OLED force recovery...\r\n");
    
    // 步骤1: 强制I2C总线恢复
    MyPrintf_DMA("DIAG: Step 1 - I2C bus recovery\r\n");
    I2C_OLED_i2c_sda_unlock();
    Delay(100);
    
    // 步骤2: 重置I2C外设
    MyPrintf_DMA("DIAG: Step 2 - I2C peripheral reset\r\n");
    DL_I2C_reset(I2C_OLED_INST);
    Delay(50);
    SYSCFG_DL_I2C_OLED_init(); // 重新初始化I2C
    Delay(100);
    
    // 步骤3: 尝试重新初始化OLED
    MyPrintf_DMA("DIAG: Step 3 - OLED reinitialization\r\n");
    bool recovery_success = OLED_Init();
    
    if (recovery_success) {
        oled_diag.reinit_count++;
        MyPrintf_DMA("DIAG: OLED recovery successful!\r\n");
        
        // 显示恢复成功信息
        I2C_OLED_Clear();
        OLED_ShowString(0, 0, 8, "Recovery OK!");
        OLED_Printf(0, 16, 8, "Count: %d", oled_diag.reinit_count);
        Delay(1000);
        I2C_OLED_Clear();
        
    } else {
        MyPrintf_DMA("DIAG: OLED recovery failed!\r\n");
    }
    
    return recovery_success;
}

/**
 * @brief 打印OLED诊断报告
 */
void OLED_PrintDiagnosticReport(void)
{
    MyPrintf_DMA("=== OLED Diagnostic Report ===\r\n");
    MyPrintf_DMA("Total Checks: %d\r\n", oled_diag.total_checks);
    MyPrintf_DMA("Success Count: %d\r\n", oled_diag.success_count);
    MyPrintf_DMA("Failure Count: %d\r\n", oled_diag.failure_count);
    MyPrintf_DMA("Success Rate: %.2f%%\r\n", oled_diag.success_rate);
    MyPrintf_DMA("Consecutive Failures: %d\r\n", oled_diag.consecutive_failures);
    MyPrintf_DMA("Max Consecutive Failures: %d\r\n", oled_diag.max_consecutive_failures);
    MyPrintf_DMA("Reinit Count: %d\r\n", oled_diag.reinit_count);
    MyPrintf_DMA("I2C Timeout Count: %d\r\n", oled_diag.i2c_timeout_count);
    MyPrintf_DMA("Is Stable: %s\r\n", oled_diag.is_stable ? "YES" : "NO");
    MyPrintf_DMA("Last Success: %d ms ago\r\n", uwTick - oled_diag.last_success_time);
    MyPrintf_DMA("Last Failure: %d ms ago\r\n", uwTick - oled_diag.last_failure_time);
    MyPrintf_DMA("==============================\r\n");
}

/**
 * @brief OLED健康检查任务
 * @details 定期检查OLED状态，自动恢复
 */
void OLED_HealthCheckTask(void)
{
    static uint32_t last_check_time = 0;
    static uint32_t last_report_time = 0;
    uint32_t current_time = uwTick;
    
    // 每1秒检查一次
    if (current_time - last_check_time >= 1000) {
        last_check_time = current_time;
        
        bool oled_ok = OLED_DiagnosticCheck();
        
        // 如果连续失败超过5次，尝试强制恢复
        if (oled_diag.consecutive_failures >= 5) {
            MyPrintf_DMA("DIAG: Too many consecutive failures, attempting recovery...\r\n");
            OLED_ForceRecovery();
        }
    }
    
    // 每30秒打印一次诊断报告
    if (current_time - last_report_time >= 30000) {
        last_report_time = current_time;
        OLED_PrintDiagnosticReport();
    }
}

/**
 * @brief 获取OLED诊断状态
 * @return OLED诊断状态结构体指针
 */
const OLED_Diagnostic_t* OLED_GetDiagnosticStatus(void)
{
    return &oled_diag;
}

/**
 * @brief 重置OLED诊断统计
 */
void OLED_ResetDiagnosticStats(void)
{
    memset(&oled_diag, 0, sizeof(oled_diag));
    MyPrintf_DMA("DIAG: OLED diagnostic statistics reset\r\n");
}

/**
 * @brief OLED压力测试
 * @param test_duration_ms 测试持续时间（毫秒）
 */
void OLED_StressTest(uint32_t test_duration_ms)
{
    MyPrintf_DMA("DIAG: Starting OLED stress test for %d ms...\r\n", test_duration_ms);
    
    uint32_t start_time = uwTick;
    uint32_t test_count = 0;
    
    while ((uwTick - start_time) < test_duration_ms) {
        test_count++;
        
        // 快速检查OLED状态
        bool present = OLED_IsPresent();
        
        if (present) {
            // 尝试显示测试内容
            OLED_Printf(0, 0, 8, "Test: %d", test_count);
            OLED_Printf(0, 16, 8, "Time: %d", uwTick - start_time);
        }
        
        Delay(100); // 100ms间隔
        
        // 每1000次测试打印一次状态
        if (test_count % 1000 == 0) {
            MyPrintf_DMA("DIAG: Stress test progress - %d tests completed\r\n", test_count);
        }
    }
    
    MyPrintf_DMA("DIAG: Stress test completed - %d tests in %d ms\r\n", 
                 test_count, uwTick - start_time);
    OLED_PrintDiagnosticReport();
}
