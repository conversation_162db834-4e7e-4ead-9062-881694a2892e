################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

CG_TOOL_ROOT := C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS

GEN_OPTS__FLAG := @"./device.opt" 
GEN_CMDS__FLAG := -Wl,-l"./device_linker.cmd" 

ORDERED_OBJS += \
"./ti_msp_dl_config.o" \
"./startup_mspm0g350x_ticlang.o" \
"./main.o" \
"./test_init_order.o" \
"./test_oled_dependency.o" \
"./test_oled_motor_display.o" \
"./APP/Src/Interrupt.o" \
"./APP/Src/Task_App.o" \
"./BSP/Src/ADC.o" \
"./BSP/Src/Key_Led.o" \
"./BSP/Src/MPU6050.o" \
"./BSP/Src/Motor.o" \
"./BSP/Src/No_Mcu_Ganv_Grayscale_Sensor.o" \
"./BSP/Src/OLED.o" \
"./BSP/Src/OLED_Font.o" \
"./BSP/Src/PID.o" \
"./BSP/Src/PID_IQMath.o" \
"./BSP/Src/Serial.o" \
"./BSP/Src/SysTick.o" \
"./BSP/Src/Task.o" \
"./BSP/Src/Tracker.o" \
"./DMP/inv_mpu.o" \
"./DMP/inv_mpu_dmp_motion_driver.o" \
$(GEN_CMDS__FLAG) \
-Wl,-ldevice.cmd.genlibs \
-Wl,-llibc.a \

-include ../makefile.init

RM := DEL /F
RMDIR := RMDIR /S/Q

# All of the sources participating in the build are defined here
-include sources.mk
-include subdir_vars.mk
-include APP/Src/subdir_vars.mk
-include BSP/Src/subdir_vars.mk
-include DMP/subdir_vars.mk
-include subdir_rules.mk
-include APP/Src/subdir_rules.mk
-include BSP/Src/subdir_rules.mk
-include DMP/subdir_rules.mk
-include objects.mk

ifneq ($(MAKECMDGOALS),clean)
ifneq ($(strip $(C55_DEPS)),)
-include $(C55_DEPS)
endif
ifneq ($(strip $(C_UPPER_DEPS)),)
-include $(C_UPPER_DEPS)
endif
ifneq ($(strip $(S67_DEPS)),)
-include $(S67_DEPS)
endif
ifneq ($(strip $(S62_DEPS)),)
-include $(S62_DEPS)
endif
ifneq ($(strip $(S_DEPS)),)
-include $(S_DEPS)
endif
ifneq ($(strip $(OPT_DEPS)),)
-include $(OPT_DEPS)
endif
ifneq ($(strip $(C??_DEPS)),)
-include $(C??_DEPS)
endif
ifneq ($(strip $(ASM_UPPER_DEPS)),)
-include $(ASM_UPPER_DEPS)
endif
ifneq ($(strip $(S??_DEPS)),)
-include $(S??_DEPS)
endif
ifneq ($(strip $(C64_DEPS)),)
-include $(C64_DEPS)
endif
ifneq ($(strip $(CXX_DEPS)),)
-include $(CXX_DEPS)
endif
ifneq ($(strip $(S64_DEPS)),)
-include $(S64_DEPS)
endif
ifneq ($(strip $(INO_DEPS)),)
-include $(INO_DEPS)
endif
ifneq ($(strip $(CLA_DEPS)),)
-include $(CLA_DEPS)
endif
ifneq ($(strip $(S55_DEPS)),)
-include $(S55_DEPS)
endif
ifneq ($(strip $(SV7A_DEPS)),)
-include $(SV7A_DEPS)
endif
ifneq ($(strip $(C62_DEPS)),)
-include $(C62_DEPS)
endif
ifneq ($(strip $(C67_DEPS)),)
-include $(C67_DEPS)
endif
ifneq ($(strip $(PDE_DEPS)),)
-include $(PDE_DEPS)
endif
ifneq ($(strip $(K_DEPS)),)
-include $(K_DEPS)
endif
ifneq ($(strip $(C_DEPS)),)
-include $(C_DEPS)
endif
ifneq ($(strip $(CC_DEPS)),)
-include $(CC_DEPS)
endif
ifneq ($(strip $(C++_DEPS)),)
-include $(C++_DEPS)
endif
ifneq ($(strip $(C43_DEPS)),)
-include $(C43_DEPS)
endif
ifneq ($(strip $(S43_DEPS)),)
-include $(S43_DEPS)
endif
ifneq ($(strip $(ASM_DEPS)),)
-include $(ASM_DEPS)
endif
ifneq ($(strip $(S_UPPER_DEPS)),)
-include $(S_UPPER_DEPS)
endif
ifneq ($(strip $(CPP_DEPS)),)
-include $(CPP_DEPS)
endif
ifneq ($(strip $(SA_DEPS)),)
-include $(SA_DEPS)
endif
endif

-include ../makefile.defs

# Add inputs and outputs from these tool invocations to the build variables 
EXE_OUTPUTS += \
TI_CAR1.out 

EXE_OUTPUTS__QUOTED += \
"TI_CAR1.out" 


# All Target
all: $(OBJS) $(GEN_CMDS)
	@$(MAKE) --no-print-directory -Onone "TI_CAR1.out"

# Tool invocations
TI_CAR1.out: $(OBJS) $(GEN_CMDS)
	@echo 'Building target: "$@"'
	@echo 'Invoking: Arm Linker'
	"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/bin/tiarmclang.exe" @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -gdwarf-3 -w -Wl,-m"TI_CAR1.map" -Wl,-i"C:/ti/mspm0_sdk_2_04_00_06/source" -Wl,-i"C:/Users/<USER>/Desktop/T2-003" -Wl,-i"C:/Users/<USER>/Desktop/T2-003/Debug/syscfg" -Wl,-i"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib" -Wl,--diag_wrap=off -Wl,--display_error_number -Wl,--warn_sections -Wl,--xml_link_info="TI_CAR1_linkInfo.xml" -Wl,--rom_model -o "TI_CAR1.out" $(ORDERED_OBJS)
	@echo 'Finished building target: "$@"'
	@echo ' '

# Other Targets
clean:
	-$(RM) $(GEN_MISC_FILES__QUOTED)$(GEN_FILES__QUOTED)$(EXE_OUTPUTS__QUOTED)
	-$(RM) "ti_msp_dl_config.o" "startup_mspm0g350x_ticlang.o" "main.o" "test_init_order.o" "test_oled_dependency.o" "test_oled_motor_display.o" "APP\Src\Interrupt.o" "APP\Src\Task_App.o" "BSP\Src\ADC.o" "BSP\Src\Key_Led.o" "BSP\Src\MPU6050.o" "BSP\Src\Motor.o" "BSP\Src\No_Mcu_Ganv_Grayscale_Sensor.o" "BSP\Src\OLED.o" "BSP\Src\OLED_Font.o" "BSP\Src\PID.o" "BSP\Src\PID_IQMath.o" "BSP\Src\Serial.o" "BSP\Src\SysTick.o" "BSP\Src\Task.o" "BSP\Src\Tracker.o" "DMP\inv_mpu.o" "DMP\inv_mpu_dmp_motion_driver.o" 
	-$(RM) "ti_msp_dl_config.d" "startup_mspm0g350x_ticlang.d" "main.d" "test_init_order.d" "test_oled_dependency.d" "test_oled_motor_display.d" "APP\Src\Interrupt.d" "APP\Src\Task_App.d" "BSP\Src\ADC.d" "BSP\Src\Key_Led.d" "BSP\Src\MPU6050.d" "BSP\Src\Motor.d" "BSP\Src\No_Mcu_Ganv_Grayscale_Sensor.d" "BSP\Src\OLED.d" "BSP\Src\OLED_Font.d" "BSP\Src\PID.d" "BSP\Src\PID_IQMath.d" "BSP\Src\Serial.d" "BSP\Src\SysTick.d" "BSP\Src\Task.d" "BSP\Src\Tracker.d" "DMP\inv_mpu.d" "DMP\inv_mpu_dmp_motion_driver.d" 
	-@echo 'Finished clean'
	-@echo ' '

.PHONY: all clean dependents
.SECONDARY:

-include ../makefile.targets

